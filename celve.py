#coding:gbk
from datetime import datetime
import time
import logging

# 简化日志配置 - 只记录重要事件
logging.basicConfig(level=logging.WARNING, format='%(asctime)s %(message)s', datefmt='%H:%M:%S')

# ==================== 回测配置参数 ====================
# 回测模式配置
BACKTEST_CONFIG = {
    'enable_backtest': True,           # 启用回测模式
    'backtest_start_date': '20250825', # 回测开始日期
    'backtest_end_date': '20250825',   # 回测结束日期
    'download_history_data': True,     # 自动下载历史数据
}

# ==================== 价格分层工具类 ====================
class PriceTierManager:
    """价格分层管理器 - 实现价格自适应参数系统"""

    def __init__(self, param_manager):
        self.pm = param_manager

        # 价格分层定义
        self.price_tiers = {
            'ultra_low': {'min': 0, 'max': 0.01},      # 极低价位 <0.01元
            'low': {'min': 0.01, 'max': 0.05},         # 低价位 0.01-0.05元
            'medium': {'min': 0.05, 'max': 0.20},      # 中价位 0.05-0.20元
            'high': {'min': 0.20, 'max': 1.0},         # 高价位 0.20-1.0元
            'ultra_high': {'min': 1.0, 'max': 999}     # 超高价位 >1.0元
        }

        print("🎯 价格分层管理器已初始化")
        self._print_tier_config()

    def _print_tier_config(self):
        """打印价格分层配置"""
        print("📊 价格分层配置:")
        for tier, config in self.price_tiers.items():
            print(f"   {tier}: {config['min']:.3f} - {config['max']:.3f}元")

    def get_price_tier(self, price):
        """获取价格所属分层"""
        for tier, config in self.price_tiers.items():
            if config['min'] <= price < config['max']:
                return tier
        return 'ultra_high'  # 默认返回最高分层

    def get_tier_risk_params(self, price):
        """获取分层风险控制参数"""
        tier = self.get_price_tier(price)
        tier_params = self.pm.get('price_tier_risk_params', {})
        return tier_params.get(tier, tier_params.get('high', {}))

    def get_min_profit_requirement(self, price):
        """计算平衡的最小盈利要求（现实性与盈利性并重）"""
        # 使用平衡的盈利比例参数
        price_ratios = self.pm.get('balanced_profit_ratios', {})

        if price < 0.01:
            return price_ratios.get('below_0.01', 0.12)  # 12%（平衡现实性与盈利性）
        elif price < 0.05:
            return price_ratios.get('below_0.05', 0.06)  # 6%（恢复盈利能力）
        elif price < 0.20:
            return price_ratios.get('below_0.20', 0.035) # 3.5%（平衡盈利与现实性）
        else:
            return price_ratios.get('above_0.20', 0.008) # 0.8%（适度提高）

    def should_filter_by_price(self, price):
        """基于价格进行过滤判断"""
        min_price = self.pm.get('min_option_price', 0.005)
        if price < min_price:
            return True, f"价格过低({price:.4f} < {min_price:.4f})"

        # 检查手续费比例
        commission = self.pm.get('commission_per_trade', 3.4)
        multiplier = self.pm.get('option_multiplier', 10000)
        max_commission_ratio = self.pm.get('max_commission_ratio', 0.15)

        entry_cost = price * multiplier
        commission_ratio = commission / entry_cost

        if commission_ratio > max_commission_ratio:
            return True, f"手续费比例过高({commission_ratio:.1%} > {max_commission_ratio:.1%})"

        return False, "价格过滤通过"

    def get_dynamic_vwap_thresholds(self, price, volatility=None):
        """获取收紧的VWAP阈值（提高信号质量）"""
        tier = self.get_price_tier(price)
        # 使用收紧的阈值参数
        tightened_thresholds = self.pm.get('tightened_vwap_thresholds', {})
        base_thresholds = tightened_thresholds.get(tier, tightened_thresholds.get('high', {}))

        # 波动率调整
        if volatility is not None and self.pm.get('enable_volatility_adjustment', True):
            vol_range = self.pm.get('volatility_multiplier_range', [0.5, 2.0])
            vol_multiplier = max(vol_range[0], min(vol_range[1], volatility / 2.0))

            return {
                'a_min': base_thresholds.get('a_min', 0.5) * vol_multiplier,
                'a_max': base_thresholds.get('a_max', 5.0) * vol_multiplier,
                'd_threshold': base_thresholds.get('d_threshold', 2.0) * vol_multiplier
            }

        return base_thresholds

    def assess_option_characteristics(self, option_code, current_price, underlying_price=None):
        """评估期权特性并返回策略建议"""
        tier = self.get_price_tier(current_price)

        # 基于价格分层的期权特性评估
        if tier == 'ultra_low':
            option_type = 'deep_otm'
            strategy_mode = 'speculative'
            risk_level = 'very_high'
        elif tier == 'low':
            option_type = 'otm_tradable'
            strategy_mode = 'aggressive'
            risk_level = 'high'
        elif tier == 'medium':
            option_type = 'near_money'
            strategy_mode = 'balanced'
            risk_level = 'medium'
        elif tier == 'high':
            option_type = 'itm_or_atm'
            strategy_mode = 'conservative'
            risk_level = 'low'
        else:  # ultra_high
            option_type = 'deep_itm'
            strategy_mode = 'ultra_conservative'
            risk_level = 'very_low'

        return {
            'price_tier': tier,
            'option_type': option_type,
            'strategy_mode': strategy_mode,
            'risk_level': risk_level,
            'recommended_position_size': self._get_position_size(risk_level),
            'risk_multiplier': self._get_risk_multiplier(risk_level)
        }

    def _get_position_size(self, risk_level):
        """根据风险等级获取建议仓位"""
        position_sizes = {
            'very_low': 1.0,
            'low': 1.0,
            'medium': 1.0,
            'high': 1.0,
            'very_high': 1.0  # 保持单张，通过其他方式控制风险
        }
        return position_sizes.get(risk_level, 1.0)

    def _get_risk_multiplier(self, risk_level):
        """根据风险等级获取风险系数"""
        risk_multipliers = {
            'very_low': 0.5,
            'low': 0.8,
            'medium': 1.0,
            'high': 1.5,
            'very_high': 3.0
        }
        return risk_multipliers.get(risk_level, 1.0)

# ==================== 参数管理模块 ====================
class ParameterManager:
    """参数管理器 - 回测简化版本"""
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ParameterManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if ParameterManager._initialized:
            return
        ParameterManager._initialized = True

        # 核心可配置参数（回测模式使用内置参数）
        self.params = {
            # 核心配置参数
            'underlying_code': '510300.SH',    # 标的代码
            'signal_threshold': 5,             # 连续同方向tick数量

            # 震荡检测参数
            'oscillation_period_size': 5,      # 每周期tick数量
            'oscillation_periods': 3,          # 需要的周期数量

            # 合约选择参数
            'min_days_to_expire': 7,           # 最少剩余天数

            # 交易参数
            'max_position_per_contract': 1,    # 单合约最大持仓量
            'order_timeout_seconds': 30,       # 委托超时时间(秒)
            'enable_real_trading': False,      # 回测模式关闭真实交易

            # 期权交易参数（新增）
            'option_multiplier': 10000,        # 期权合约乘数
            'commission_per_trade': 3.4,       # 每笔交易手续费（元）
            'price_equal_tolerance': 0.0001,   # 价格相等容忍度

            # 回测专用参数
            'backtest_mode': True,             # 回测模式标识
            'test_option_code': '********.SHO', # 测试期权代码

            # ==================== 价格自适应参数系统 ====================
            # 期权价格过滤参数
            'min_option_price': 0.005,         # 最低期权价格5厘，避免极端低价期权
            'max_commission_ratio': 0.15,      # 手续费不超过成本15%
            'enable_price_tier_filter': True,  # 启用价格分层过滤

            # 相对盈利模式参数
            'use_relative_profit': True,       # 启用相对盈利模式
            'balanced_profit_ratios': {        # 平衡的盈利要求（现实性与盈利性并重）
                'below_0.01': 0.12,           # 12%盈利（从15%适度降低，平衡现实性）
                'below_0.05': 0.06,           # 6%盈利（从5%适度提高，恢复盈利能力）
                'below_0.20': 0.035,          # 3.5%盈利（从2.5%提高，平衡盈利与现实性）
                'above_0.20': 0.008           # 0.8%盈利（从0.6%适度提高）
            },

            # 价格分层风险控制参数
            'ultra_low_price_multiplier': 3.0, # 极低价期权的风险系数
            'price_tier_risk_params': {
                'ultra_low': {                 # <0.01元
                    'max_loss_ratio': -25.0,      # 最大亏损比例25%
                    'quick_exit_time': 30,        # 30秒快速退出
                    'enable_vwap_protection': False,  # 禁用VWAP保护
                    'min_profit_for_hold': 5.0    # 最小盈利5%才继续持有
                },
                'low': {                       # 0.01-0.05元
                    'max_loss_ratio': -15.0,
                    'quick_exit_time': 60,
                    'enable_vwap_protection': True,
                    'min_profit_for_hold': 3.0
                },
                'medium': {                    # 0.05-0.20元
                    'max_loss_ratio': -10.0,
                    'quick_exit_time': 90,
                    'enable_vwap_protection': True,
                    'min_profit_for_hold': 2.0
                },
                'high': {                      # 0.20-1.0元
                    'max_loss_ratio': -8.0,
                    'quick_exit_time': 240,        # 恢复原策略的4分钟
                    'enable_vwap_protection': True,
                    'min_profit_for_hold': 1.0
                },
                'ultra_high': {                # >1.0元
                    'max_loss_ratio': -6.0,
                    'quick_exit_time': 150,
                    'enable_vwap_protection': True,
                    'min_profit_for_hold': 0.8
                }
            },

            # VWAP过滤器参数 - 基于77.csv/78.csv分析优化
            'vwap_filter_enabled': True,       # VWAP过滤器总开关
            'vwap_a_enabled': True,            # A类条件开关
            'vwap_a_min': 0.5,                 # A类最小百分比 (降低至0.5%，适应强势行情)
            'vwap_a_max': 5.0,                 # A类最大百分比 (提高至5.0%，适应强势行情)
            'vwap_b_enabled': True,            # B类排除开关
            'vwap_b_threshold': 2.0,           # B类回撤阈值 (保持: 2.0%)
            'vwap_d_enabled': False,           # D类条件开关（暂时禁用C类信号）
            'vwap_d_threshold': 2.0,           # D类反弹阈值 (优化: 2.0% - 提高C类信号质量)
            'vwap_e_enabled': False,           # E类条件开关（暂时禁用C类信号）
            # E类：基于局部低点的反弹区间
            'vwap_e_min': 2.0,                 # E类最小反弹百分比（%）
            'vwap_e_max': 3.0,                 # E类最大反弹百分比（%）
            'vwap_debug_log': False,           # VWAP过滤器调试日志（已关闭以减少GUI日志体量）
            'vwap_c_max_diff_below': 5.0,     # C类放行时距VWAP的最大负偏离（%），超过则不买入
            'vwap_slope_window': 120,          # 计算VWAP斜率的窗口（tick）
            'vwap_slope_min': 0.0,             # 允许的最小VWAP斜率（%/窗口），小于该值拒绝C类
            'c_bounce_verify_window_secs': 180,# C类反弹验证窗口（秒），窗口内未达最小浮盈则提前退出
            'c_bounce_min_profit_for_protect': 5.0, # C类在验证窗口内判断“曾经盈利”的阈值（元）
            'vwap_a_max_trend': 8.0,          # 趋势日放宽A类最大百分比
            'vwap_trend_slope_strong': 0.2,    # 视为上升趋势日的VWAP斜率阈值（%/窗口）

            # ================= 趋势跟踪信号参数 (新增) =================
            'enable_trend_following_buy': True,    # 是否启用趋势跟踪信号
            'trend_following_min_slope': 0.1,      # 趋势跟踪要求的最小VWAP斜率 (%/窗口)
            'trend_following_max_diff_pct': 1.5,   # 趋势跟踪模式下，允许价格高于VWAP的最大幅度(%)
            # =========================================================

            # ================= 趋势跟踪信号参数 (新增) =================
            'enable_trend_following_buy': True,    # 是否启用趋势跟踪信号
            'trend_following_min_slope': 0.1,      # 趋势跟踪要求的最小VWAP斜率 (%/窗口)
            'trend_following_max_diff_pct': 1.5,   # 趋势跟踪模式下，允许价格高于VWAP的最大幅度(%)
            # ==========================================================

            # 收紧的VWAP阈值参数（提高信号质量，避免低质量信号通过）
            'tightened_vwap_thresholds': {
                'ultra_low': {'a_min': 3.0, 'a_max': 8.0, 'd_threshold': 6.0},   # 大幅收紧，提高信号质量
                'low': {'a_min': 2.0, 'a_max': 6.0, 'd_threshold': 4.0},         # 大幅收紧
                'medium': {'a_min': 1.5, 'a_max': 4.0, 'd_threshold': 2.5},      # 大幅收紧
                'high': {'a_min': 0.8, 'a_max': 3.0, 'd_threshold': 2.0},        # 适度收紧
                'ultra_high': {'a_min': 0.5, 'a_max': 2.0, 'd_threshold': 1.5}   # 适度收紧
            },

            # 波动率自适应参数
            'enable_volatility_adjustment': True,  # 启用波动率调整
            'volatility_window': 50,               # 波动率计算窗口
            'volatility_multiplier_range': [0.5, 2.0],  # 波动率调整系数范围

            # 增强VWAP保护缓冲区（基于实际波动特性调整）
            'vwap_protection_buffer': {
                'ultra_low': 0.03,    # 3%缓冲区（原2%不足，增大50%）
                'low': 0.025,         # 2.5%缓冲区（原1.5%不足，增大67%）
                'medium': 0.02,       # 2%缓冲区（原1%不足，翻倍）
                'high': 0.015,        # 1.5%缓冲区（原0.8%不足，增大87%）
                'ultra_high': 0.01    # 1%缓冲区（原0.5%不足，翻倍）
            },

            # 趋势强度识别和动态止盈参数（第一轮优化）
            'trend_strength_enabled': True,      # 启用趋势强度识别
            'trend_strength_threshold': 0.015,   # 1.5%涨幅视为强势趋势（降低0.5%）
            'strong_trend_profit_multiplier': 1.25, # 强势趋势中提高止盈目标25%（适度降低）
            'trend_time_extension': 45,          # 强势趋势中延长持仓时间45秒（适度降低）

            # 波动率自适应参数
            'volatility_adaptive_enabled': True,  # 启用波动率自适应
            'high_volatility_threshold': 0.04,   # 4%波动率视为高波动
            'high_vol_buffer_multiplier': 1.5,   # 高波动时增大缓冲区50%
            'low_vol_profit_multiplier': 1.2,    # 低波动时提高止盈目标20%

            # A类验证机制优化参数（解决验证失败亏损问题）
            'a_class_verification_enhanced': {
                'max_verification_time': 90,      # 最大验证时间90秒（vs当前121秒）
                'early_exit_threshold': -2.0,     # -2%时提前退出
                'min_profit_maintain_time': 20,   # 最少维持盈利20秒
                'verification_strictness': 1.2    # 验证严格度系数
            },

            # VWAP稳定性检查参数（第二轮优化 - 提高信号质量）
            'vwap_stability_check': {
                'enabled': True,
                'min_stable_periods': 3,         # 至少3个周期的稳定趋势
                'max_volatility': 0.03,          # 最大波动率3%（适度宽松）
                'trend_consistency': 0.7         # 70%的趋势一致性（适度宽松）
            },

            # 自适应市场环境识别参数
            'adaptive_mode_enabled': False,      # 暂时禁用复杂自适应模式
            'simple_market_detection': False,    # 禁用复杂检测器，使用价格变化判断
            'market_trend_window': 200,          # 市场趋势判断窗口（tick数）
            'strong_uptrend_threshold': 0.15,    # 强上升趋势VWAP斜率阈值（进一步降低以更容易触发）
            'weak_uptrend_threshold': 0.1,       # 弱上升趋势VWAP斜率阈值
            'downtrend_threshold': -0.1,         # 下跌趋势VWAP斜率阈值
            'volatility_window': 100,            # 波动率计算窗口
            'high_volatility_threshold': 2.5,    # 高波动率阈值（%）
            'low_volatility_threshold': 1.0,     # 低波动率阈值（%）

            # 震荡/趋势日行为参数（保留原有）
            'slope_threshold_flat': 0.05,       # |VWAP斜率|<该值视为震荡/平斜率日（%/窗口）
            'vwap_a_min_flat': 1.2,             # 震荡日A类最小高于VWAP百分比
            'vwap_a_max_flat': 2.0,             # 震荡日A类最大高于VWAP百分比
            'a_verify_window_secs': 120,        # A类验证窗口：窗口内未“曾经盈利达阈值”则退出
            'a_min_profit_for_protect': 8.0,    # A类验证窗口内“曾经盈利”金额阈值（元）
            'a_min_profit_ratio_for_protect': 1.8, # A类验证窗口内“曾经盈利”比例阈值（%）
            'a_fail_below_vwap_buffer': 0.2,    # A类失败：低于入场VWAP的缓冲（%）
            'a_fail_below_hold_secs': 15,       # A类失败：低于阈值需持续的秒数
            'cooldown_after_exit_secs': 60,     # 平仓后买入冷却时间（秒）
            'a_retrace_to_zero_hold_secs': 30, # A类在曾经盈利后回撤到不盈利需持续的秒数
            'a_time_stop_secs': 300,           # A类中途失败时间止损（秒）- 5分钟平衡设置
            'a_time_stop_loss_amt': 10.0,      # A类时间止损的亏损金额阈值（元）- 10元平衡设置


        }

        # 硬编码参数（不需要配置）
        self.fixed_params = {
            # 数据过滤参数
            'enable_duplicate_filter': True,   # 启用重复tick过滤
            'price_precision': 4,              # 价格精度

            # 价格链参数
            'max_chain_length': 30,            # 价格链最大长度
            'display_timestamp': True,         # 显示时间戳

            # 趋势检测参数
            'enable_trend_detection': True,    # 启用趋势检测
            'reset_after_signal': True,        # 信号触发后重置

            # 期权选择参数
            'select_call_count': 1,            # 选择1个认购期权
            'select_put_count': 1,             # 选择1个认沽期权
            'prefer_nearest_expiry': True,     # 优先最近到期
            'prefer_nearest_strike': True,     # 优先最近行权价

            # 日志参数
            'enable_tick_log': False,          # 禁用详细tick日志
            'enable_signal_log': True,         # 启用信号日志
        }

        print("?? 回测模式：使用内置参数，无需XML配置文件")

        # 显示关键参数
        print(f"?? 核心参数: 信号阈值={self.params['signal_threshold']}, 测试期权={self.params['test_option_code']}")

        # 显示VWAP过滤器参数
        if self.params.get('vwap_filter_enabled', True):
            print(f"?? VWAP过滤器: A类({self.params['vwap_a_min']}-{self.params['vwap_a_max']}%) "
                  f"B类排除(≥{self.params['vwap_b_threshold']}%) D类(≥{self.params['vwap_d_threshold']}%)")


    def get(self, param_name, default_value=None):
        """获取参数值（先查找可配置参数，再查找硬编码参数）"""
        if param_name in self.params:
            return self.params[param_name]
        elif param_name in self.fixed_params:
            return self.fixed_params[param_name]
        else:
            return default_value

    def set(self, param_name, value):
        """设置参数值"""
        self.params[param_name] = value

    def print_params(self):
        """打印所有参数"""
        print("=== 回测模式参数配置 ===")
        print("?? 可配置参数:")
        for key, value in self.params.items():
            print(f"  {key} = {value}")
        print("?? 硬编码参数:")
        for key, value in self.fixed_params.items():
            print(f"  {key} = {value}")
        print("==================")

# ==================== 平仓策略模块 ====================
class OptimalExitStrategy:
    """价格自适应三层防护平仓策略"""

    def __init__(self, param_manager=None, market_analyzer=None):
        # 参数管理器和市场分析器
        self.pm = param_manager
        self.market_analyzer = market_analyzer

        # 初始化价格分层管理器
        self.price_tier_manager = PriceTierManager(self.pm) if self.pm else None

        # ==================== 基础参数（将被价格自适应系统覆盖） ====================
        # 第一层：快速止盈参数 (基础设置，实际使用时会根据价格分层调整)
        self.quick_profit_threshold = 25.0    # 基础快速止盈阈值(25元)
        self.quick_profit_ratio = 2.5         # 基础快速止盈涨幅(2.5%)
        self.quick_exit_ratio = 1.0           # 快速止盈平仓比例(100%)
        self.quick_time_limit = 240           # 基础快速止盈时间限制(4分钟)

        # 第二层：动态跟踪参数
        self.tracking_start_time = 240        # 动态跟踪开始时间(4分钟)
        self.tracking_end_time = 720          # 动态跟踪结束时间(12分钟)
        self.tracking_profit_threshold = 20.0 # 基础动态跟踪启动盈利阈值(20元)
        self.tracking_ratio = 0.65            # 基础动态跟踪比例(35%回撤)

        # 第三层：保护性平仓参数
        self.protection_start_time = 600      # 保护性平仓开始时间(10分钟)
        self.protection_exit_time = 720       # 保护性平仓触发时间(12分钟)
        self.protection_exit_ratio = 1.0      # 保护性平仓比例(100%)

        # 通用最大持仓时间
        self.max_holding_time = 1080          # 基础最大持仓时间(18分钟)

        # 止损参数 (基于分析优化 - 将根据价格分层动态调整)
        self.stop_loss_ratio = -5.0           # 基础止损比例(-5%)
        self.stop_loss_amount = -30.0         # 基础止损金额(-30元)
        self.enable_stop_loss = False         # 基础止损开关

        print(f"🎯 初始化价格自适应三层防护平仓策略")
        if self.market_analyzer:
            print(f"   ✅ 已集成市场环境分析器，支持动态参数调整")
        if self.price_tier_manager:
            print(f"   ✅ 已集成价格分层管理器，支持价格自适应调整")
        print(f"   第一层快速止盈: 基础{self.quick_time_limit}秒内, 盈利≥{self.quick_profit_threshold}元或涨幅≥{self.quick_profit_ratio}%")
        print(f"   第二层动态跟踪: {self.tracking_start_time}-{self.tracking_end_time}秒, 盈利≥{self.tracking_profit_threshold}元时启动")
        print(f"   第三层保护平仓: {self.protection_start_time}秒后, {self.protection_exit_time}秒时平仓")
        print(f"   强制平仓: 基础{self.max_holding_time}秒后强制全部平仓")
        print(f"   🔄 价格自适应模式: 根据期权价格水平实时调整所有参数")

    def calculate_profit(self, current_price, buy_price, quantity=1):
        """计算盈利金额和比例（使用配置化参数）"""
        # 获取配置参数（添加安全检查）
        if self.pm is not None:
            multiplier = self.pm.get('option_multiplier', 10000)
            commission = self.pm.get('commission_per_trade', 3.4)
        else:
            # 默认参数
            multiplier = 10000
            commission = 3.4

        # 佣金按“每份合约的往返总费用”计（默认3.4元/份，双向合计），按本次平仓数量线性计提
        commission_total = commission * quantity
        # 实际盈亏 = (卖价-买价)*合约乘数*数量 - 往返佣金
        profit_amount = (current_price - buy_price) * multiplier * quantity - commission_total
        # 实际盈亏率 = 含手续费净收益 / 入场名义本金（不含佣金）
        entry_notional = buy_price * multiplier * quantity
        profit_ratio = (profit_amount / entry_notional * 100) if entry_notional > 0 else 0.0

        return profit_amount, profit_ratio

    def get_price_adaptive_params(self, batch, current_price):
        """价格自适应参数系统 - 根据期权价格水平动态调整所有参数"""
        entry_price = batch['entry_price']
        current_profit = batch.get('max_profit', 0)
        holding_seconds = self.get_holding_seconds(batch)

        # 获取价格分层信息
        if self.price_tier_manager:
            tier_info = self.price_tier_manager.assess_option_characteristics(
                batch.get('option_code', ''), entry_price
            )
            tier_risk_params = self.price_tier_manager.get_tier_risk_params(entry_price)
            min_profit_requirement = self.price_tier_manager.get_min_profit_requirement(entry_price)
        else:
            # 回退到基础参数
            tier_info = {'price_tier': 'high', 'risk_level': 'medium', 'risk_multiplier': 1.0}
            tier_risk_params = {}
            min_profit_requirement = 0.025

        # 计算合约价值
        contract_value = entry_price * 10000

        # 基于价格分层的参数调整
        price_tier = tier_info['price_tier']
        risk_multiplier = tier_info['risk_multiplier']

        # 动态调整止盈阈值（智能混合模式）
        if self.pm and self.pm.get('use_relative_profit', True):
            # 对于高价位期权（>0.20元），保持接近原策略的固定金额逻辑
            if price_tier in ['high', 'ultra_high']:
                # 使用原策略的固定金额，但根据价格适度调整
                base_threshold = 25.0  # 原策略的25元基准
                price_adjustment = max(0.8, min(1.5, entry_price / 0.25))  # 以0.25元为基准调整
                quick_profit_threshold = base_threshold * price_adjustment
                quick_profit_ratio = (quick_profit_threshold / contract_value) * 100
                tracking_profit_threshold = quick_profit_threshold * 0.8
            else:
                # 对于低价位期权，使用相对盈利比例
                quick_profit_ratio = min_profit_requirement * 3  # 提高倍数以避免过早平仓
                quick_profit_threshold = contract_value * quick_profit_ratio
                tracking_profit_threshold = contract_value * min_profit_requirement * 2
        else:
            # 使用固定金额（按风险系数调整）
            quick_profit_threshold = self.quick_profit_threshold / risk_multiplier
            quick_profit_ratio = self.quick_profit_ratio
            tracking_profit_threshold = self.tracking_profit_threshold / risk_multiplier

        # 现实化的时间参数（匹配盈利目标的可达性）
        realistic_time_windows = {
            'ultra_low': {'quick': 120, 'max': 600},    # 2分钟快速，10分钟最大（给足时间积累盈利）
            'low': {'quick': 180, 'max': 720},          # 3分钟快速，12分钟最大
            'medium': {'quick': 240, 'max': 900},       # 4分钟快速，15分钟最大
            'high': {'quick': 300, 'max': 1080},        # 5分钟快速，18分钟最大（略微延长）
            'ultra_high': {'quick': 360, 'max': 1200}   # 6分钟快速，20分钟最大
        }

        time_config = realistic_time_windows.get(price_tier, realistic_time_windows['high'])
        quick_time_limit = time_config['quick']
        max_holding_time = time_config['max']

        # 趋势强度识别和动态调整
        trend_multiplier = 1.0
        volatility_multiplier = 1.0

        if self.pm.get('trend_strength_enabled', True):
            # 简化的趋势强度计算（基于当前盈利状况）
            if current_profit > 0:
                profit_ratio = current_profit / contract_value
                trend_threshold = self.pm.get('trend_strength_threshold', 0.02)

                if profit_ratio >= trend_threshold:
                    # 检测到强势趋势，提高止盈目标和延长时间
                    trend_multiplier = self.pm.get('strong_trend_profit_multiplier', 1.3)
                    time_extension = self.pm.get('trend_time_extension', 60)
                    quick_time_limit += time_extension
                    max_holding_time += time_extension
                    print(f"🚀 强势趋势检测: 盈利{profit_ratio:.1%} >= {trend_threshold:.1%}, 调整系数{trend_multiplier:.1f}")

        # 应用趋势调整
        if 'quick_profit_threshold' in locals():
            quick_profit_threshold *= trend_multiplier
        if 'tracking_profit_threshold' in locals():
            tracking_profit_threshold *= trend_multiplier

        # 动态调整风险控制参数
        max_loss_ratio = tier_risk_params.get('max_loss_ratio', self.stop_loss_ratio)
        enable_vwap_protection = tier_risk_params.get('enable_vwap_protection', True)

        print(f"🎯 价格自适应评估: 价格{entry_price:.4f}元, 分层={price_tier}, 风险系数={risk_multiplier:.1f}")
        print(f"   最小盈利要求: {min_profit_requirement:.1%}, 快速止盈: {quick_profit_threshold:.1f}元")
        print(f"   时间参数: 快速{quick_time_limit}秒, 最大{max_holding_time}秒")

        return {
            'price_tier': price_tier,
            'risk_multiplier': risk_multiplier,
            'quick_profit_threshold': quick_profit_threshold,
            'quick_profit_ratio': quick_profit_ratio if 'quick_profit_ratio' in locals() else min_profit_requirement * 2 * 100,
            'quick_time_limit': quick_time_limit,
            'max_holding_time': max_holding_time,
            'tracking_profit_threshold': tracking_profit_threshold,
            'tracking_ratio': max(0.3, self.tracking_ratio - (risk_multiplier - 1) * 0.1),
            'max_loss_ratio': max_loss_ratio,
            'enable_vwap_protection': enable_vwap_protection,
            'strategy_mode': f"price_adaptive_{price_tier}",
            'min_profit_requirement': min_profit_requirement
        }

    def get_universal_params(self, batch, current_price):
        """通用参数系统 - 简单有效，避免过度拟合"""
        entry_price = batch['entry_price']
        current_profit = batch.get('max_profit', 0)
        holding_seconds = self.get_holding_seconds(batch)

        # 计算合约价值（用于相对计算）
        contract_value = entry_price * 10000

        # 简单的表现评估（避免复杂分类）
        profit_ratio = current_profit / contract_value if contract_value > 0 else 0

        # 通用自适应逻辑：基于实际表现动态调整（优化版）
        if profit_ratio >= 0.025 or current_profit >= 30:  # 表现优秀
            mode = 'performing_excellent'
            multiplier = 1.8  # 更激进，抓住强势行情
        elif profit_ratio >= 0.015 or current_profit >= 20:  # 表现良好
            mode = 'performing_well'
            multiplier = 1.4  # 适度激进
        elif profit_ratio >= 0.008 or current_profit >= 10:  # 表现一般
            mode = 'performing_average'
            multiplier = 1.1  # 略微激进
        else:  # 表现不佳
            mode = 'performing_poor'
            multiplier = 0.9  # 轻微保守

        print(f"🎯 通用评估: 盈利比例{profit_ratio:.3f}, 当前盈利{current_profit:.1f}元, 模式={mode}")

        # 返回调整后的参数
        return {
            'quick_profit_threshold': self.quick_profit_threshold * multiplier,
            'quick_profit_ratio': self.quick_profit_ratio * multiplier,
            'quick_time_limit': int(self.quick_time_limit * multiplier),
            'max_holding_time': int(self.max_holding_time * multiplier),
            'tracking_ratio': max(0.5, self.tracking_ratio - (multiplier - 1) * 0.05),  # 更宽松的回撤调整
            'strategy_mode': mode
        }

    def get_adaptive_market_params_old(self, batch, current_price):
        """自适应多模式市场参数系统 - 解决过度拟合问题"""
        entry_price = batch['entry_price']

        # 计算基础指标
        current_profit_pct = ((current_price - entry_price) / entry_price) * 100
        max_profit = batch.get('max_profit', 0)
        holding_seconds = self.get_holding_seconds(batch)

        # 计算合约价值（用于相对阈值）
        contract_value = entry_price * 10000  # 期权合约价值

        # 市场环境分析
        market_condition = self.analyze_market_condition(batch, current_price, entry_price)

        # 获取自适应阈值
        adaptive_thresholds = self.get_adaptive_thresholds(entry_price, market_condition)

        print(f"🔍 市场分析: 入场价{entry_price:.4f} -> 当前价{current_price:.4f}")
        print(f"🔍 合约价值: {contract_value:.0f}元, 当前盈利{current_profit_pct:.2f}%, 最大盈利{max_profit:.1f}元")
        print(f"🔍 市场环境: {market_condition['type']}, 波动性: {market_condition['volatility']:.3f}")

        # 多模式策略选择
        strategy_mode = self.select_strategy_mode(
            current_profit_pct, max_profit, contract_value,
            market_condition, adaptive_thresholds, holding_seconds
        )

        # 返回对应的参数配置
        return self.get_mode_specific_params(strategy_mode, adaptive_thresholds, market_condition)

    def get_holding_seconds(self, batch):
        """获取持仓时间（秒）"""
        entry_time = batch.get('time')
        if entry_time:
            try:
                if isinstance(entry_time, str):
                    from datetime import datetime
                    entry_dt = datetime.strptime(entry_time, '%Y-%m-%d %H:%M:%S')
                    holding_seconds = (datetime.now() - entry_dt).total_seconds()
                else:
                    import time
                    holding_seconds = time.time() - entry_time
                return max(0, holding_seconds)
            except:
                return 60  # 默认60秒
        return 60

    def analyze_market_condition(self, batch, current_price, entry_price):
        """分析市场环境条件"""

        # 获取价格历史数据（从批次中获取）
        price_history = batch.get('price_history', [entry_price, current_price])
        if len(price_history) < 2:
            price_history = [entry_price, current_price]

        # 计算波动性指标
        if len(price_history) >= 3:
            price_changes = []
            for i in range(1, len(price_history)):
                change_pct = abs((price_history[i] - price_history[i-1]) / price_history[i-1]) * 100
                price_changes.append(change_pct)
            volatility = sum(price_changes) / len(price_changes) if price_changes else 0.1
        else:
            volatility = abs((current_price - entry_price) / entry_price) * 100

        # 计算趋势强度
        if len(price_history) >= 3:
            trend_direction = 0
            for i in range(1, len(price_history)):
                if price_history[i] > price_history[i-1]:
                    trend_direction += 1
                elif price_history[i] < price_history[i-1]:
                    trend_direction -= 1
            trend_strength = abs(trend_direction) / (len(price_history) - 1)
        else:
            trend_strength = 1.0 if current_price != entry_price else 0.0

        # 分类市场环境（针对8月26日优化）
        if volatility > 1.5:  # 高波动性（提高阈值）
            if trend_strength > 0.6:
                market_type = 'high_vol_trending'
            else:
                market_type = 'high_vol_oscillating'
        elif volatility > 0.8:  # 中等波动性（新增分类）
            if trend_strength > 0.5:
                market_type = 'medium_vol_trending'
            else:
                market_type = 'medium_vol_oscillating'
        else:  # 低波动性
            if trend_strength > 0.3:  # 降低趋势阈值
                market_type = 'low_vol_trending'
            else:
                market_type = 'low_vol_flat'

        return {
            'type': market_type,
            'volatility': volatility,
            'trend_strength': trend_strength,
            'price_level': entry_price  # 用于价格区间判断
        }

    def get_adaptive_thresholds(self, entry_price, market_condition):
        """计算自适应阈值 - 基于合约价值和市场条件"""

        contract_value = entry_price * 10000  # 期权合约价值
        volatility = market_condition['volatility']
        price_level = market_condition['price_level']

        # 基础相对阈值（基于合约价值的百分比）
        base_profit_ratio = 0.03  # 合约价值的3%作为基础盈利阈值
        base_change_ratio = 0.008  # 0.8%作为基础价格变化阈值

        # 根据价格区间调整（针对8月26日优化）
        if price_level < 0.12:  # 低价区间（如8月21日的0.09元）
            price_adjustment = 1.8  # 大幅提高敏感度
        elif price_level < 0.20:  # 中低价区间
            price_adjustment = 1.3  # 适中提高敏感度
        elif price_level < 0.28:  # 中高价区间（如8月25日、26日的0.23-0.25元）
            price_adjustment = 1.0  # 标准敏感度
        else:  # 高价区间
            price_adjustment = 0.7  # 降低敏感度

        # 根据波动性调整
        if volatility > 1.5:  # 高波动性
            volatility_adjustment = 1.2  # 提高阈值
        elif volatility < 0.5:  # 低波动性
            volatility_adjustment = 0.8  # 降低阈值
        else:
            volatility_adjustment = 1.0

        # 计算最终阈值
        profit_threshold = contract_value * base_profit_ratio * price_adjustment * volatility_adjustment
        price_change_threshold = base_change_ratio * price_adjustment * volatility_adjustment

        return {
            'profit_threshold': max(profit_threshold, 5.0),  # 最小5元
            'price_change_threshold': max(price_change_threshold, 0.3),  # 最小0.3%
            'contract_value': contract_value,
            'adjustments': {
                'price_adj': price_adjustment,
                'vol_adj': volatility_adjustment
            }
        }

    def select_strategy_mode(self, current_profit_pct, max_profit, contract_value,
                           market_condition, adaptive_thresholds, holding_seconds):
        """多模式策略选择 - 避免过度拟合"""

        profit_threshold = adaptive_thresholds['profit_threshold']
        price_change_threshold = adaptive_thresholds['price_change_threshold']
        market_type = market_condition['type']
        volatility = market_condition['volatility']
        trend_strength = market_condition['trend_strength']

        # 多维度评分系统
        performance_score = 0

        # 盈利表现评分（相对于合约价值）
        profit_ratio = max_profit / contract_value if contract_value > 0 else 0
        if profit_ratio >= 0.05:  # 5%以上
            performance_score += 3
        elif profit_ratio >= 0.03:  # 3-5%
            performance_score += 2
        elif profit_ratio >= 0.01:  # 1-3%
            performance_score += 1

        # 价格变化评分
        if abs(current_profit_pct) >= price_change_threshold * 2:
            performance_score += 2
        elif abs(current_profit_pct) >= price_change_threshold:
            performance_score += 1

        # 市场环境评分（更新以支持新分类）
        if market_type in ['high_vol_trending', 'medium_vol_trending', 'low_vol_trending']:
            performance_score += 2  # 趋势环境加分
        elif market_type in ['high_vol_oscillating', 'medium_vol_oscillating']:
            performance_score += 1  # 波动震荡适中
        else:  # low_vol_flat
            performance_score += 0  # 平盘环境不加分

        # 趋势强度评分
        if trend_strength >= 0.7:
            performance_score += 2
        elif trend_strength >= 0.4:
            performance_score += 1

        # 根据综合评分选择模式（针对8月26日优化）
        if performance_score >= 7:  # 提高激进模式门槛
            strategy_mode = 'aggressive_trending'
        elif performance_score >= 5:  # 提高适中模式门槛
            strategy_mode = 'moderate_performance'
        elif performance_score >= 3:  # 提高保守模式门槛
            strategy_mode = 'conservative_stable'
        else:
            strategy_mode = 'defensive_mode'

        # 特殊处理：低波动平盘环境强制使用防御模式
        if market_type == 'low_vol_flat' or (volatility < 0.5 and trend_strength < 0.3):
            strategy_mode = 'defensive_mode'
            print(f"⚠️ 低波动平盘环境检测，强制使用防御模式")

        print(f"🎯 策略选择: 评分{performance_score}/9, 模式={strategy_mode}")
        print(f"   盈利比例: {profit_ratio:.3f}, 价格变化: {current_profit_pct:.2f}%")
        print(f"   市场类型: {market_type}, 趋势强度: {trend_strength:.2f}")

        return strategy_mode

    def get_mode_specific_params(self, strategy_mode, adaptive_thresholds, market_condition):
        """获取模式特定的参数配置"""

        contract_value = adaptive_thresholds['contract_value']
        base_profit_threshold = adaptive_thresholds['profit_threshold']
        volatility = market_condition['volatility']

        # 基础时间参数（根据市场波动性调整）
        if volatility > 1.5:  # 高波动性
            base_time_multiplier = 0.8  # 缩短持仓时间
        elif volatility < 0.5:  # 低波动性
            base_time_multiplier = 1.3  # 延长持仓时间
        else:
            base_time_multiplier = 1.0

        if strategy_mode == 'aggressive_trending':
            # 激进趋势模式：适用于强趋势高表现情况
            return {
                'quick_profit_threshold': base_profit_threshold * 2.5,  # 2.5倍基础阈值
                'quick_profit_ratio': 4.0,                              # 4%止盈
                'quick_time_limit': int(360 * base_time_multiplier),    # 6分钟
                'max_holding_time': int(900 * base_time_multiplier),    # 15分钟
                'tracking_ratio': 0.3,                                  # 70%回撤容忍
                'strategy_mode': strategy_mode
            }

        elif strategy_mode == 'moderate_performance':
            # 适中表现模式：平衡风险和收益
            return {
                'quick_profit_threshold': base_profit_threshold * 1.8,  # 1.8倍基础阈值
                'quick_profit_ratio': 3.0,                              # 3%止盈
                'quick_time_limit': int(240 * base_time_multiplier),    # 4分钟
                'max_holding_time': int(600 * base_time_multiplier),    # 10分钟
                'tracking_ratio': 0.4,                                  # 60%回撤容忍
                'strategy_mode': strategy_mode
            }

        elif strategy_mode == 'conservative_stable':
            # 保守稳定模式：优先保护资金
            return {
                'quick_profit_threshold': base_profit_threshold * 1.2,  # 1.2倍基础阈值
                'quick_profit_ratio': 2.0,                              # 2%止盈
                'quick_time_limit': int(180 * base_time_multiplier),    # 3分钟
                'max_holding_time': int(450 * base_time_multiplier),    # 7.5分钟
                'tracking_ratio': 0.5,                                  # 50%回撤容忍
                'strategy_mode': strategy_mode
            }

        else:  # defensive_mode
            # 防御模式：针对8月26日优化的超保守参数设置
            return {
                'quick_profit_threshold': base_profit_threshold * 0.6,  # 0.6倍基础阈值（更低）
                'quick_profit_ratio': 1.0,                              # 1.0%止盈（更低）
                'quick_time_limit': int(90 * base_time_multiplier),     # 1.5分钟（更短）
                'max_holding_time': int(240 * base_time_multiplier),    # 4分钟（更短）
                'tracking_ratio': 0.7,                                  # 30%回撤容忍（更严格）
                'strategy_mode': strategy_mode
            }

    def check_exit_signal(self, batch, current_price, current_time):
        """检查平仓信号

        Args:
            batch: 批次信息，包含买入价格、数量、时间等
            current_price: 当前价格
            current_time: 当前时间

        Returns:
            tuple: (exit_type, exit_ratio)
                   exit_type: 'none', 'quick_profit', 'dynamic_tracking', 'protection', 'force_exit', 'stop_loss', 'c_bounce_fail', 'a_verify_fail', 'a_breakdown_below_vwap'
                   exit_ratio: 平仓比例 0.0-1.0
        """

        print(f"🔍 开始平仓检查: 批次#{batch.get('batch_id', 'N/A')} 当前价{current_price:.4f}")

        buy_price = batch['price']
        buy_time = batch['time']
        quantity = batch.get('remaining_quantity', batch['quantity'])

        # 使用价格自适应参数系统（优先）或通用参数系统（回退）
        if self.price_tier_manager:
            market_params = self.get_price_adaptive_params(batch, current_price)
            print(f"🔍 价格自适应系统: 模式={market_params.get('strategy_mode', 'adaptive')}")
        else:
            market_params = self.get_universal_params(batch, current_price)
            print(f"🔍 通用参数系统: 模式={market_params.get('strategy_mode', 'universal')}")

        # 读取进场模式，专用于C类的“验证窗口”
        entry_category = batch.get('entry_vwap_category')


        # 确保时间类型一致
        from datetime import datetime
        if isinstance(current_time, str):
            current_time = datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S')
        if isinstance(buy_time, str):
            buy_time = datetime.strptime(buy_time, '%Y-%m-%d %H:%M:%S')

        # 计算持仓时间（秒）
        holding_time = (current_time - buy_time).total_seconds()

        # 计算当前盈利
        current_profit, current_profit_ratio = self.calculate_profit(current_price, buy_price, quantity)
        # C类（低位反弹）专用的验证窗口提前退出与跌破局部低点的防御
        if entry_category == 'C_bounce':
            try:
                # 参数
                verify_window = self.pm.get('c_bounce_verify_window_secs', 180) if self.pm else 180
                min_profit_amt = self.pm.get('c_bounce_min_profit_for_protect', 5.0) if self.pm else 5.0
                # 标记达到过最小浮盈
                if current_profit >= min_profit_amt:
                    batch['c_has_reached_min_profit'] = True
                # 跌破进场时记录的局部低点，直接退出
                entry_local_low = batch.get('c_entry_local_low')
                if entry_local_low is not None and current_price <= entry_local_low:
                    return 'c_bounce_fail', 1.0
                # 验证窗口到期仍未达到最小浮盈，则退出
                if holding_time >= verify_window and not batch.get('c_has_reached_min_profit', False):
                    return 'c_bounce_fail', 1.0
            except Exception:
                pass

        # A类（向上穿越VWAP）在震荡/失败时的早切逻辑
        if entry_category == 'A':
            try:
                # A类验证窗口：窗口期内从未达到“曾经盈利阈值”则退出
                a_verify_window = self.pm.get('a_verify_window_secs', 120) if self.pm else 120
                a_min_profit_amt = self.pm.get('a_min_profit_for_protect', 5.0) if self.pm else 5.0
                a_min_profit_ratio = self.pm.get('a_min_profit_ratio_for_protect', 1.2) if self.pm else 1.2

                if current_profit >= a_min_profit_amt or current_profit_ratio >= a_min_profit_ratio:
                    batch['a_has_reached_min_profit'] = True
                if holding_time >= a_verify_window and not batch.get('a_has_reached_min_profit', False):
                    return 'a_verify_fail', 1.0

                # A类：价格跌回入场VWAP下方（使用智能缓冲区，避免过度敏感）
                entry_vwap = batch.get('entry_vwap')
                if entry_vwap is not None:
                    # 使用价格分层的智能缓冲区
                    entry_price = batch.get('entry_price', 0.1)
                    if self.price_tier_manager:
                        price_tier = self.price_tier_manager.get_price_tier(entry_price)
                        vwap_buffers = self.pm.get('vwap_protection_buffer', {})
                        buffer_pct = vwap_buffers.get(price_tier, 0.01)  # 默认1%缓冲区
                    else:
                        buffer_pct = (self.pm.get('a_fail_below_vwap_buffer', 0.2) if self.pm else 0.2) / 100.0

                    hold_secs = self.pm.get('a_fail_below_hold_secs', 15) if self.pm else 15

                    below = current_price <= float(entry_vwap) * (1 - buffer_pct)
                    if below:
                        # 第一次触发时记录时间
                        if 'a_fail_flag_time' not in batch or batch['a_fail_flag_time'] is None:
                            batch['a_fail_flag_time'] = holding_time
                            print(f"⚠️  VWAP保护触发: 价格{current_price:.4f} < VWAP{entry_vwap:.4f}*(1-{buffer_pct:.1%}) = {float(entry_vwap)*(1-buffer_pct):.4f}")
                        else:
                            if (holding_time - batch['a_fail_flag_time']) >= hold_secs:
                                print(f"🚨 VWAP保护平仓: 持续{holding_time - batch['a_fail_flag_time']}秒低于缓冲区")
                                return 'a_breakdown_below_vwap', 1.0
                    else:
                        batch['a_fail_flag_time'] = None

                # A类：曾经盈利后回撤至不盈利并持续N秒，提前小亏离场（避免被拖到强平）
                if batch.get('a_has_reached_min_profit', False):
                    # 时间闸门：仅当已超过快速止盈窗口后，才允许“回撤至不盈利”触发早切
                    if holding_time > self.quick_time_limit:
                        non_profit_secs = self.pm.get('a_retrace_to_zero_hold_secs', 10) if self.pm else 10
                        if current_profit <= 0:
                            if 'a_nonprofit_since' not in batch or batch['a_nonprofit_since'] is None:
                                batch['a_nonprofit_since'] = holding_time
                            else:
                                if (holding_time - batch['a_nonprofit_since']) >= non_profit_secs:
                                    return 'a_retrace_to_zero', 1.0
                        else:
                            batch['a_nonprofit_since'] = None

                # A类：时间止损（到达一定时间且亏损超过阈值）
                a_time_stop_secs = self.pm.get('a_time_stop_secs', 180) if self.pm else 180
                a_time_stop_loss_amt = self.pm.get('a_time_stop_loss_amt', 5.0) if self.pm else 5.0
                if holding_time >= a_time_stop_secs and current_profit <= -abs(a_time_stop_loss_amt):
                    return 'a_time_stop_loss', 1.0

            except Exception:
                pass


        # 更新最大盈利记录
        if 'max_profit' not in batch:
            batch['max_profit'] = current_profit
            batch['max_profit_ratio'] = current_profit_ratio
        else:
            if current_profit > batch['max_profit']:
                batch['max_profit'] = current_profit
                batch['max_profit_ratio'] = current_profit_ratio

        # 更新价格历史（用于市场环境分析）
        price_history = batch.get('price_history', [])
        price_history.append(current_price)
        # 保持最近20个价格点
        if len(price_history) > 20:
            price_history = price_history[-20:]
        batch['price_history'] = price_history

        # 止损检查（基于78.csv分析 - 禁用止损）
        if self.enable_stop_loss and (current_profit <= self.stop_loss_amount or current_profit_ratio <= self.stop_loss_ratio):
            return 'stop_loss', 1.0

        # 第一层：快速止盈 (使用市场差异化参数)
        quick_time_limit = market_params['quick_time_limit']
        quick_profit_threshold = market_params['quick_profit_threshold']
        quick_profit_ratio = market_params['quick_profit_ratio']
        strategy_mode = market_params.get('strategy_mode', 'balanced')

        # 通用自适应处理
        strategy_mode = market_params.get('strategy_mode', 'universal')
        print(f"🎯 {strategy_mode}模式：当前盈利{current_profit:.1f}元，持仓{holding_time:.0f}秒")

        # 第一层：快速止盈 (使用自适应参数)
        if holding_time <= quick_time_limit:
            if current_profit >= quick_profit_threshold or current_profit_ratio >= quick_profit_ratio:
                return 'quick_profit', 1.0  # 统一使用全部平仓

        # 获取市场差异化最大持仓时间
        max_holding_time = market_params['max_holding_time']

        # 第二层：动态跟踪 (2-5分钟)
        tracking_end_time = max_holding_time  # 使用自适应的最大持仓时间

        # 传统动态跟踪（仅用于非趋势跟踪模式）
        if self.tracking_start_time < holding_time <= tracking_end_time:
            # 根据策略模式调整动态跟踪参数
            tracking_ratio = market_params.get('tracking_ratio', self.tracking_ratio)

            # 使用通用自适应阈值
            tracking_threshold = market_params.get('quick_profit_threshold', self.tracking_profit_threshold) * 0.8

            # 启动条件：历史曾经盈利≥阈值
            if batch.get('max_profit', 0) >= tracking_threshold:
                # 触发条件：当前盈利低于峰值利润的一定比例（允许为负，视为“回撤过深”）
                if current_profit < batch['max_profit'] * tracking_ratio:
                    print(f"📊 通用动态跟踪: 峰值{batch.get('max_profit', 0):.1f}元 -> 当前{current_profit:.1f}元")
                    return 'dynamic_tracking', 1.0

        # 第三层：保护性平仓 (使用自适应最大持仓时间)
        elif self.protection_start_time < holding_time <= max_holding_time:
            if current_profit > 0:  # 仍有盈利
                # 时间条件：持仓时间 > 6.5分钟
                if holding_time > self.protection_exit_time:
                    return 'protection', self.protection_exit_ratio

        # 强制平仓 (使用自适应时间)
        elif holding_time > max_holding_time:
            return 'force_exit', 1.0

        return 'none', 0.0

    def get_strategy_status(self, batch, current_price, current_time):
        """获取策略状态信息（用于日志显示）"""
        buy_price = batch['price']
        buy_time = batch['time']
        quantity = batch.get('remaining_quantity', batch['quantity'])

        # 确保时间类型一致（增强异常处理）
        from datetime import datetime

        try:
            # 调试信息
            print(f"🔍 时间计算调试:")
            print(f"   current_time: {current_time} (类型: {type(current_time)})")
            print(f"   buy_time: {buy_time} (类型: {type(buy_time)})")

            # 安全的时间转换
            if isinstance(current_time, str):
                try:
                    current_time = datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S')
                    print(f"   转换后 current_time: {current_time}")
                except ValueError as e:
                    print(f"❌ current_time格式错误: {current_time}, 错误: {e}")
                    return "none", 0.0

            if isinstance(buy_time, str):
                try:
                    buy_time = datetime.strptime(buy_time, '%Y-%m-%d %H:%M:%S')
                    print(f"   转换后 buy_time: {buy_time}")
                except ValueError as e:
                    print(f"❌ buy_time格式错误: {buy_time}, 错误: {e}")
                    return "none", 0.0

            # 计算持仓时间
            holding_time = (current_time - buy_time).total_seconds()
            print(f"   计算的持仓时间: {holding_time}秒 ({holding_time/60:.1f}分钟)")

            # 检查持仓时间是否合理
            if holding_time < 0:
                print(f"❌ 警告: 持仓时间为负数 {holding_time}秒，可能存在时间计算错误")
                return "none", 0.0

        except Exception as e:
            print(f"❌ 时间计算失败: {e}")
            return "none", 0.0
        current_profit, current_profit_ratio = self.calculate_profit(current_price, buy_price, quantity)

        max_profit = batch.get('max_profit', current_profit)

        # 确定当前所在层级
        if holding_time <= self.quick_time_limit:
            layer = "第一层(快速止盈)"
        elif holding_time <= self.tracking_end_time:
            layer = "第二层(动态跟踪)"
        elif holding_time <= self.max_holding_time:
            layer = "第三层(保护平仓)"
        else:
            layer = "强制平仓区间"

        return {
            'layer': layer,
            'holding_time': holding_time,
            'current_profit': current_profit,
            'current_profit_ratio': current_profit_ratio,
            'max_profit': max_profit,
            'remaining_quantity': quantity
        }


class ReportWriter:
    """运行期生成最终报告CSV，避免依赖GUI日志截断"""
    def __init__(self, output_path):
        import os
        self.output_path = output_path
        self.rows = []
        self.batch_index = {}  # batch_id -> row index
        self.header = [
            '序号','信号类型','触发时间','买入价格','当时VWAP','价格差异','差异百分比',
            '实际执行','阻止原因','买入前持仓','买入后持仓','买入数量',
            'VWAP模式类型','买入质量','时机评估','D反弹(%)','E反弹(%)','模式分类','触发序列','震荡周期信息',
            '通过A类条件','通过D类条件','通过E类条件','被B类排除','最终通过','过滤原因',
            '平仓类型','平仓价格','持仓时间(秒)','实际盈亏(元)','实际盈亏(%)','是否盈利','平仓状态'
        ]
        # 确保目录存在
        os.makedirs(os.path.dirname(self.output_path), exist_ok=True)
        # 若已存在旧文件，先清空重写
        try:
            with open(self.output_path, 'w', encoding='utf-8-sig') as f:
                f.write(','.join(self.header) + '\n')
        except Exception:
            pass

    def _flush(self):
        import csv
        with open(self.output_path, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(self.header)
            for r in self.rows:
                writer.writerow([r.get(k, '') for k in self.header])

    def add_signal(self, row: dict, batch_id: int):
        row['序号'] = len(self.rows) + 1
        self.rows.append(row)
        if batch_id is not None:
            self.batch_index[batch_id] = len(self.rows) - 1
        self._flush()

    def update_exit(self, batch_id: int, exit_fields: dict):
        idx = self.batch_index.get(batch_id)
        if idx is None:
            return
        self.rows[idx].update(exit_fields)
        self._flush()


# ==================== 数据存储模块 ====================
class OptionBatchManager:
    """期权批次管理类 - 价格自适应版本（内存存储）"""
    def __init__(self):
        # 回测模式使用内存存储，不需要持久化
        self.batches = []
        self.pending_orders = {}  # 待成交委托
        self.pm = ParameterManager()  # 参数管理器

        # 初始化价格分层管理器
        self.price_tier_manager = PriceTierManager(self.pm)

        # 初始化市场检测器
        detection_enabled = self.pm.get('simple_market_detection', False)
        print(f"🔍 调试：simple_market_detection参数值 = {detection_enabled}")

        if detection_enabled:
            self.market_detector = SimpleMarketDetector(self.pm)
            print("🎯 已启用简化市场检测器")
        else:
            self.market_detector = None
            print("⚠️ 市场检测已禁用，使用固定参数")

        # 保留复杂自适应分析器（暂时禁用）
        if self.pm.get('adaptive_mode_enabled', False):
            self.market_analyzer = AdaptiveMarketAnalyzer(self.pm)
            print("🎯 已启用自适应市场分析器")
        else:
            self.market_analyzer = None

        # 初始化平仓策略（集成价格分层管理器和市场检测器）
        self.exit_strategy = OptimalExitStrategy(self.pm, self.market_analyzer)
        # 传递市场检测器给平仓策略
        if hasattr(self, 'market_detector'):
            self.exit_strategy.market_detector = self.market_detector

        print("🎯 回测模式：使用内存存储，已集成价格自适应系统")
        print("🎯 已集成价格自适应三层防护平仓策略")

    def add_pending_order(self, order_id, option_code, target_price, target_quantity):
        """记录待成交委托"""
        self.pending_orders[order_id] = {
            'option_code': option_code,
            'target_price': target_price,
            'target_quantity': target_quantity,
            'timestamp': time.time(),
            'status': 'pending'
        }
        print(f"?? 记录委托: {order_id} {option_code} 目标价格:{target_price:.4f} 数量:{target_quantity}")

    def add_batch_from_signal(self, option_code, signal_type, price, quantity=1, timestamp=None):
        """
        从信号记录模拟成交批次（回测版本）- 价格自适应版本
        """
        from datetime import datetime

        # 价格过滤检查
        if self.price_tier_manager:
            should_filter, filter_reason = self.price_tier_manager.should_filter_by_price(price)
            if should_filter:
                print(f"❌ 价格过滤拒绝: {option_code} 价格{price:.4f}元 - {filter_reason}")
                return None  # 不创建批次

        # 获取真实的历史时间戳（回测模式下）
        if timestamp:
            try:
                real_time = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                trade_time_str = real_time.strftime('%H:%M:%S')
                trade_date_str = real_time.strftime('%Y-%m-%d')
                batch_time = real_time
                print(f"✅ 使用历史时间戳: {timestamp} -> {batch_time}")
            except ValueError as e:
                print(f"❌ 时间戳格式错误: {timestamp}, 错误: {e}")
                print(f"❌ 回测模式下时间戳格式必须为 'YYYY-MM-DD HH:MM:SS'")
                raise ValueError(f"时间戳格式错误: {timestamp}")
        else:
            # 检查是否在回测模式下
            is_backtest = getattr(self, 'is_backtest_mode', False) or hasattr(self, '_current_real_timestamp')
            if is_backtest:
                print(f"❌ 严重错误: 回测模式下timestamp不能为空！")
                print(f"❌ 这会导致持仓时间计算错误，策略失效！")
                raise ValueError("回测模式下timestamp不能为空！这会导致时间计算错误。")

            # 实盘模式：使用当前时间
            real_time = datetime.now()
            trade_time_str = real_time.strftime('%H:%M:%S')
            trade_date_str = real_time.strftime('%Y-%m-%d')
            batch_time = real_time
            print(f"✅ 实盘模式: 使用当前时间: {batch_time}")

        # 期权特性评估
        option_characteristics = None
        if self.price_tier_manager:
            option_characteristics = self.price_tier_manager.assess_option_characteristics(
                option_code, float(price)
            )
            print(f"🎯 期权特性评估: {option_code} 价格{price:.4f}元")
            print(f"   分层={option_characteristics['price_tier']}, 类型={option_characteristics['option_type']}")
            print(f"   策略模式={option_characteristics['strategy_mode']}, 风险等级={option_characteristics['risk_level']}")

        batch = {
            'batch_id': len(self.batches) + 1,
            'option_code': option_code,
            'signal_type': signal_type,

            # 模拟成交信息
            'entry_price': float(price),
            'price': float(price),  # 兼容平仓策略
            'quantity': int(quantity),
            'remaining_quantity': int(quantity),  # 剩余数量
            'cost': float(price) * int(quantity) * self.pm.get('option_multiplier', 10000),  # 期权合约乘数
            'commission': self.pm.get('commission_per_trade', 3.4),  # 固定手续费

            # 时间信息（使用真实历史时间）
            'trade_time': trade_time_str,
            'trade_date': trade_date_str,
            'time': batch_time,  # 兼容平仓策略
            'record_timestamp': time.time(),

            # 数据来源标记
            'data_source': 'backtest_signal',
            'data_reliability': 'simulated',

            # 计算字段
            'total_cost': float(price) * int(quantity) * 10000 + 3.4,

            # 期权特性信息
            'option_characteristics': option_characteristics,

            # 平仓策略相关字段
            'exit_strategy': {
                'max_profit': None,
                'max_profit_ratio': None,
                'fully_closed': False,
                'exit_history': []  # 记录平仓历史
            }
        }

        self.batches.append(batch)

        print(f"?? 回测记录#{batch['batch_id']}: {option_code} {signal_type} "
              f"价格:{batch['entry_price']:.4f} 数量:{batch['quantity']}")

        return batch

    def get_batch_by_id(self, batch_id):
        """根据批次ID查询批次信息"""
        for batch in self.batches:
            if batch['batch_id'] == batch_id:
                return batch
        return None

    def get_batches_by_date(self, date_str):
        """查询指定日期的所有批次"""
        return [batch for batch in self.batches if batch['trade_date'] == date_str]

    def query_batch_price(self, batch_id):
        """查询特定批次的成交价格"""
        batch = self.get_batch_by_id(batch_id)
        if batch:
            return {
                'batch_id': batch_id,
                'option_code': batch['option_code'],
                'entry_price': batch['entry_price'],
                'quantity': batch['quantity'],
                'trade_time': f"{batch['trade_date']} {batch['trade_time']}",
                'data_source': batch['data_source'],
                'reliability': batch['data_reliability']
            }
        return None

    def show_all_batches(self):
        """显示所有批次信息"""
        if not self.batches:
            print("?? 暂无批次记录")
            return

        print(f"\n?? 回测批次记录总览 (共{len(self.batches)}个批次):")
        print("-" * 80)
        for batch in self.batches:
            signal_info = f" ({batch.get('signal_type', 'N/A')})" if 'signal_type' in batch else ""
            print(f"批次#{batch['batch_id']}: {batch['option_code']}{signal_info}")
            print(f"  成交价格: {batch['entry_price']:.4f} 元")
            print(f"  成交数量: {batch['quantity']} 张")
            print(f"  成交时间: {batch['trade_date']} {batch['trade_time']}")
            print(f"  数据来源: {batch['data_source']} (可靠性: {batch['data_reliability']})")
            print("-" * 40)

    def get_batches_for_option(self, option_code):
        """获取指定期权的所有批次"""
        return [batch for batch in self.batches if batch['option_code'] == option_code]

    def calculate_batch_pnl(self, option_code, current_price):
        """计算指定期权所有批次的盈亏"""
        batches = self.get_batches_for_option(option_code)
        batch_pnl = []

        for batch in batches:
            pnl = (current_price - batch['entry_price']) * batch['quantity']
            pnl_rate = (current_price - batch['entry_price']) / batch['entry_price']

            batch_pnl.append({
                'batch_id': batch['batch_id'],
                'entry_price': batch['entry_price'],
                'quantity': batch['quantity'],
                'timestamp': batch['timestamp'],
                'pnl': pnl,
                'pnl_rate': pnl_rate,
                'current_price': current_price
            })

        return batch_pnl

    def get_total_position(self, option_code):
        """获取指定期权的有效持仓（仅统计未完全平仓的剩余数量）"""
        batches = self.get_batches_for_option(option_code)
        total = 0
        for batch in batches:
            # 若有平仓标记，则不计入持仓
            if batch.get('exit_strategy', {}).get('fully_closed', False):
                continue
            rem = batch.get('remaining_quantity')
            if rem is None:
                rem = batch.get('quantity', 0)
            total += max(0, int(rem))
        return total

    def get_average_cost(self, option_code):
        """计算平均成本（仅用于参考）"""
        batches = self.get_batches_for_option(option_code)
        if not batches:
            return 0

        total_cost = sum(batch['cost'] for batch in batches)
        total_quantity = sum(batch['quantity'] for batch in batches)
        return total_cost / total_quantity if total_quantity > 0 else 0

    def handle_cancelled_order(self, order_id):
        """处理撤单情况"""
        if order_id in self.pending_orders:
            order_info = self.pending_orders[order_id]
            print(f"? 委托撤销: {order_id} {order_info['option_code']} "
                  f"目标价格:{order_info['target_price']:.4f} "
                  f"数量:{order_info['target_quantity']}")
            del self.pending_orders[order_id]
            return order_info
        return None

    def check_exit_signals(self, option_code, current_price, current_time):
        """检查平仓信号并执行平仓"""
        from datetime import datetime

        batches_to_check = [batch for batch in self.batches
                           if batch['option_code'] == option_code
                           and not batch['exit_strategy'].get('fully_closed', False)]

        exit_actions = []

        for batch in batches_to_check:
            # 跳过已完全平仓的批次
            if batch['exit_strategy'].get('fully_closed', False):
                continue

            # 检查平仓信号
            try:
                exit_signal, exit_ratio = self.exit_strategy.check_exit_signal(batch, current_price, current_time)
                print(f"🔍 平仓检查结果: 批次#{batch['batch_id']} 信号={exit_signal} 比例={exit_ratio}")
            except Exception as e:
                print(f"❌ 平仓策略检查异常: 批次#{batch['batch_id']} 错误={e}")
                import traceback
                traceback.print_exc()
                continue  # 跳过这个批次，继续检查其他批次

            if exit_signal != 'none':
                # 计算平仓数量
                remaining_qty = batch.get('remaining_quantity', batch['quantity'])

                # 单张持仓时：任何比例都全平；多张时按比例，且兜底至少1张
                if remaining_qty <= 1:
                    exit_quantity = 1
                else:
                    exit_quantity = int(remaining_qty * exit_ratio)
                    if exit_quantity == 0:
                        exit_quantity = 1

                if exit_quantity > 0:
                    # 执行平仓
                    self.execute_exit(batch, exit_signal, exit_quantity, current_price, current_time)

                    exit_actions.append({
                        'batch_id': batch['batch_id'],
                        'exit_signal': exit_signal,
                        'exit_quantity': exit_quantity,
                        'remaining_quantity': remaining_qty - exit_quantity,
                        'exit_price': current_price
                    })

        return exit_actions

    def execute_exit(self, batch, exit_signal, exit_quantity, exit_price, exit_time):
        """执行平仓操作"""
        from datetime import datetime

        # 更新剩余数量
        batch['remaining_quantity'] = batch.get('remaining_quantity', batch['quantity']) - exit_quantity

        # 计算盈亏（统一调用平仓策略的计算，包含手续费）
        buy_price = batch['price']
        profit_amount, profit_ratio = self.exit_strategy.calculate_profit(exit_price, buy_price, exit_quantity)

        # 记录平仓历史
        exit_record = {
            'exit_time': exit_time,
            'exit_signal': exit_signal,
            'exit_quantity': exit_quantity,
            'exit_price': exit_price,
            'profit_amount': profit_amount,
            'profit_ratio': profit_ratio
        }

        batch['exit_strategy']['exit_history'].append(exit_record)

        # 检查是否完全平仓
        if batch['remaining_quantity'] <= 0:
            batch['exit_strategy']['fully_closed'] = True

        # 打印平仓信息
        strategy_status = self.exit_strategy.get_strategy_status(batch, exit_price, exit_time)

        print(f"?? 平仓执行 - 批次#{batch['batch_id']} {batch['option_code']}")
        print(f"   平仓类型: {exit_signal}")
        print(f"   平仓数量: {exit_quantity} (剩余: {batch['remaining_quantity']})")
        print(f"   平仓价格: {exit_price:.4f} (买入价格: {buy_price:.4f})")
        print(f"   盈亏金额: {profit_amount:.2f}元 ({profit_ratio:+.2f}%)")
        print(f"   持仓时间: {strategy_status['holding_time']:.0f}秒 ({strategy_status['layer']})")

        return exit_record

    def get_exit_strategy_status(self, option_code, current_price, current_time):
        """获取平仓策略状态（用于监控）"""
        batches = [batch for batch in self.batches
                  if batch['option_code'] == option_code
                  and not batch['exit_strategy'].get('fully_closed', False)]

        status_list = []
        for batch in batches:
            status = self.exit_strategy.get_strategy_status(batch, current_price, current_time)
            status['batch_id'] = batch['batch_id']
            status_list.append(status)

        return status_list

    def clear_option_batches(self, option_code):
        """清除指定期权的所有批次记录"""
        try:
            original_count = len(self.batches)
            self.batches = [batch for batch in self.batches if batch['option_code'] != option_code]
            removed_count = original_count - len(self.batches)

            if removed_count > 0:
                print(f"?? 已清除 {option_code} 的 {removed_count} 个批次记录")
            else:
                print(f"?? {option_code} 无批次记录需要清除")

        except Exception as e:
            print(f"? 清除批次记录失败: {option_code} {e}")

class OptionDataStore:
    """数据存储类 - 价格自适应版本"""
    def __init__(self, param_manager):
        self.pm = param_manager

        # 系统配置（从参数管理器获取，可通过XML配置）
        self.underlying_code = self.pm.get('underlying_code')
        self.selected_options = []

        # 数据存储
        self.last_prices = {}
        self.last_tick_time = {}
        self.price_chains = {}
        self.trend_count = {}
        self.trend_direction = {}
        self.trend_prices = {}
        self.signals = {}

        # 震荡检测数据
        self.oscillation_data = {}
        self.current_tick_id = {}  # 跟踪每个合约的tick ID

        # 交易相关数据 - 使用价格自适应批次管理器
        self.batch_manager = OptionBatchManager()
        self.pending_buy_quantities = {}  # 记录未完成的买入数量

        print("🎯 数据存储类已集成价格自适应系统")

# ==================== 简化市场环境检测模块 ====================
class SimpleMarketDetector:
    """简化的市场环境检测器 - 只识别三种状态：强上升、下跌、震荡"""

    def __init__(self, param_manager=None):
        self.pm = param_manager
        self.vwap_history = []
        self.price_history = []
        self.current_state = 'oscillation'  # 默认震荡状态
        self.state_change_count = 0

        print("🎯 简化市场检测器已启动")

    def add_data(self, price, vwap):
        """添加价格和VWAP数据"""
        self.price_history.append(price)
        self.vwap_history.append(vwap)

        # 保持窗口大小
        max_window = 100
        if len(self.price_history) > max_window:
            self.price_history = self.price_history[-max_window:]
            self.vwap_history = self.vwap_history[-max_window:]

        # 每20个tick检测一次市场状态
        if len(self.vwap_history) >= 20 and len(self.vwap_history) % 20 == 0:
            new_state = self.detect_market_state()
            if new_state != self.current_state:
                print(f"📊 市场状态变化: {self.current_state} → {new_state}")
                self.current_state = new_state
                self.state_change_count += 1

        return self.current_state

    def detect_market_state(self):
        """检测当前市场状态"""
        if len(self.vwap_history) < 20:
            return 'oscillation'

        # 计算VWAP斜率（最近60个tick）
        window = min(60, len(self.vwap_history))
        recent_vwap = self.vwap_history[-window:]

        if len(recent_vwap) < 2:
            return 'oscillation'

        first_vwap = recent_vwap[0]
        last_vwap = recent_vwap[-1]

        if first_vwap > 0:
            vwap_slope = ((last_vwap - first_vwap) / first_vwap) * 100
        else:
            return 'oscillation'

        # 计算价格变化率
        recent_prices = self.price_history[-window:]
        if len(recent_prices) >= 2:
            first_price = recent_prices[0]
            last_price = recent_prices[-1]
            if first_price > 0:
                price_change = ((last_price - first_price) / first_price) * 100
            else:
                price_change = 0
        else:
            price_change = 0

        # 简单的三状态判断
        strong_threshold = self.pm.get('strong_uptrend_threshold', 0.15) if self.pm else 0.15
        down_threshold = self.pm.get('downtrend_threshold', -0.1) if self.pm else -0.1

        # 强上升：VWAP斜率 > 0.15% 或 价格变化 > 0.8%
        if vwap_slope >= strong_threshold or price_change >= 0.8:
            return 'strong_uptrend'
        # 下跌：VWAP斜率 < -0.1%
        elif vwap_slope <= down_threshold:
            return 'downtrend'
        # 其他情况：震荡
        else:
            return 'oscillation'

    def get_current_state(self):
        """获取当前市场状态"""
        return self.current_state

# ==================== 自适应市场环境识别模块 ====================
class AdaptiveMarketAnalyzer:
    """自适应市场环境识别器 - 实时判断市场状态并动态调整策略参数"""

    def __init__(self, param_manager=None):
        self.pm = param_manager
        self.price_history = []
        self.vwap_history = []
        self.volume_history = []
        self.current_market_state = 'neutral'
        self.last_state_change_time = None

        # 市场状态统计
        self.state_duration = {'strong_uptrend': 0, 'weak_uptrend': 0, 'downtrend': 0, 'oscillation': 0}

        print("🎯 自适应市场分析器已启动")

    def add_market_data(self, price, vwap, volume, timestamp):
        """添加市场数据并分析环境"""
        self.price_history.append(price)
        self.vwap_history.append(vwap)
        self.volume_history.append(volume)

        # 保持数据窗口大小
        max_window = self.pm.get('market_trend_window', 200) if self.pm else 200
        if len(self.price_history) > max_window:
            self.price_history = self.price_history[-max_window:]
            self.vwap_history = self.vwap_history[-max_window:]
            self.volume_history = self.volume_history[-max_window:]

        # 分析市场环境
        new_state = self.analyze_market_environment()
        if new_state != self.current_market_state:
            print(f"📊 市场环境变化: {self.current_market_state} → {new_state}")
            self.current_market_state = new_state
            self.last_state_change_time = timestamp

        return self.current_market_state

    def analyze_market_environment(self):
        """分析当前市场环境"""
        if len(self.vwap_history) < 50:
            return 'neutral'

        # 计算VWAP趋势斜率
        vwap_slope = self.calculate_vwap_slope()

        # 计算价格波动率
        volatility = self.calculate_volatility()

        # 计算价格相对VWAP的位置分布
        vwap_position_ratio = self.calculate_vwap_position_ratio()

        # 计算价格变化率（作为强上升趋势的补充判断）
        price_change_rate = self.calculate_price_change_rate()

        # 添加调试信息（简化版）
        strong_threshold = self.pm.get('strong_uptrend_threshold', 0.15)
        if len(self.vwap_history) % 50 == 0:  # 每50个tick输出一次，减少日志量
            print(f"🔍 市场分析: VWAP斜率={vwap_slope:.3f}%, 价格变化率={price_change_rate:.3f}%, 阈值={strong_threshold}%")

        # 综合判断市场环境（使用双重条件：VWAP斜率 OR 价格变化率）
        is_strong_uptrend = (vwap_slope >= strong_threshold) or (price_change_rate >= 0.8)  # 价格上涨0.8%也算强上升

        if is_strong_uptrend:
            if volatility > self.pm.get('high_volatility_threshold', 2.5):
                print(f"🚀 识别为强上升+高波动趋势 (VWAP斜率={vwap_slope:.3f}%, 价格变化={price_change_rate:.3f}%)")
                return 'strong_uptrend_high_vol'  # 强上升+高波动
            else:
                print(f"🚀 识别为强上升趋势 (VWAP斜率={vwap_slope:.3f}%, 价格变化={price_change_rate:.3f}%)")
                return 'strong_uptrend'  # 强上升+低波动
        elif vwap_slope >= self.pm.get('weak_uptrend_threshold', 0.1):
            return 'weak_uptrend'
        elif vwap_slope <= self.pm.get('downtrend_threshold', -0.1):
            return 'downtrend'
        else:
            if volatility > self.pm.get('high_volatility_threshold', 2.5):
                return 'high_volatility_oscillation'  # 高波动震荡
            else:
                return 'low_volatility_oscillation'   # 低波动震荡

    def calculate_vwap_slope(self):
        """计算VWAP斜率"""
        if len(self.vwap_history) < 20:
            return 0.0

        window = min(60, len(self.vwap_history))  # 缩短窗口以更敏感地捕捉趋势
        recent_vwap = self.vwap_history[-window:]

        if len(recent_vwap) < 2:
            return 0.0

        first_vwap = recent_vwap[0]
        last_vwap = recent_vwap[-1]

        if first_vwap > 0:
            slope = ((last_vwap - first_vwap) / first_vwap) * 100
            return slope
        return 0.0

    def calculate_volatility(self):
        """计算价格波动率"""
        if len(self.price_history) < 20:
            return 0.0

        window = min(self.pm.get('volatility_window', 100), len(self.price_history))
        recent_prices = self.price_history[-window:]

        if len(recent_prices) < 2:
            return 0.0

        # 计算价格变化率的标准差
        price_changes = []
        for i in range(1, len(recent_prices)):
            change = (recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1] * 100
            price_changes.append(abs(change))

        if price_changes:
            return sum(price_changes) / len(price_changes)
        return 0.0

    def calculate_vwap_position_ratio(self):
        """计算价格相对VWAP位置的分布比例"""
        if len(self.price_history) < 20 or len(self.vwap_history) < 20:
            return 0.5

        window = min(50, len(self.price_history))
        above_count = 0

        for i in range(-window, 0):
            if self.price_history[i] > self.vwap_history[i]:
                above_count += 1

        return above_count / window

    def calculate_price_change_rate(self):
        """计算价格变化率（作为趋势判断的补充）"""
        if len(self.price_history) < 20:
            return 0.0

        window = min(60, len(self.price_history))  # 与VWAP斜率窗口一致
        recent_prices = self.price_history[-window:]

        if len(recent_prices) < 2:
            return 0.0

        first_price = recent_prices[0]
        last_price = recent_prices[-1]

        if first_price > 0:
            change_rate = ((last_price - first_price) / first_price) * 100
            return change_rate
        return 0.0

    def get_adaptive_parameters(self):
        """根据市场环境返回自适应参数"""
        state = self.current_market_state

        if state == 'strong_uptrend':
            return {
                'vwap_a_min': 0.8,
                'vwap_a_max': 5.0,
                'quick_profit_threshold': 50.0,      # 大幅提高至50元
                'quick_profit_ratio': 8.0,           # 大幅提高至8%
                'quick_time_limit': 600,             # 延长至10分钟
                'max_holding_time': 1200,            # 延长至20分钟
                'strategy_mode': 'super_aggressive_trend_following'
            }
        elif state == 'strong_uptrend_high_vol':
            return {
                'vwap_a_min': 1.2,
                'vwap_a_max': 3.5,
                'quick_profit_threshold': 15.0,
                'quick_profit_ratio': 3.0,
                'quick_time_limit': 240,
                'max_holding_time': 480,
                'strategy_mode': 'moderate_trend_following'
            }
        elif state == 'weak_uptrend':
            return {
                'vwap_a_min': 1.3,
                'vwap_a_max': 2.8,
                'quick_profit_threshold': 12.0,
                'quick_profit_ratio': 2.5,
                'quick_time_limit': 200,
                'max_holding_time': 420,
                'strategy_mode': 'cautious_trend_following'
            }
        elif state == 'downtrend':
            return {
                'vwap_a_min': 2.0,
                'vwap_a_max': 2.5,
                'quick_profit_threshold': 8.0,
                'quick_profit_ratio': 1.8,
                'quick_time_limit': 120,
                'max_holding_time': 300,
                'strategy_mode': 'defensive_scalping'
            }
        elif state == 'high_volatility_oscillation':
            return {
                'vwap_a_min': 1.8,
                'vwap_a_max': 2.2,
                'quick_profit_threshold': 10.0,
                'quick_profit_ratio': 2.2,
                'quick_time_limit': 150,
                'max_holding_time': 360,
                'strategy_mode': 'volatility_scalping'
            }
        else:  # low_volatility_oscillation or neutral
            return {
                'vwap_a_min': 1.5,
                'vwap_a_max': 2.5,
                'quick_profit_threshold': 12.0,
                'quick_profit_ratio': 2.5,
                'quick_time_limit': 180,
                'max_holding_time': 420,
                'strategy_mode': 'balanced'
            }

# ==================== VWAP计算模块 ====================
class VWAPPatternAnalyzer:
    """VWAP模式分析器 - 分析价格相对VWAP的模式"""

    def __init__(self, param_manager=None):
        self.pm = param_manager
        self.data = {}  # {option_code: {'price_history': [], 'vwap_history': [], 'above_vwap_high': None, 'below_vwap_low': None, 'last_vwap_state': None}}

    def add_data_point(self, option_code, price, vwap, timestamp):
        """添加新的数据点并分析模式"""
        if option_code not in self.data:
            self.data[option_code] = {
                'price_history': [],
                'vwap_history': [],
                'above_vwap_high': None,      # 高于VWAP期间的历史最高点
                'below_vwap_low': None,       # 低于VWAP期间的历史最低点
                'last_local_low': None,       # 上一个局部低点
                'is_in_rebound': False,       # 是否处于反弹状态
                'last_vwap_state': None       # 上一次的VWAP状态：'above', 'below', None
            }

        data = self.data[option_code]
        data['price_history'].append(price)
        data['vwap_history'].append(vwap)

        # 更新高低点记录
        self.update_high_low_points(option_code, price, vwap)

        # 不限制历史数据长度，让VWAP状态切换时的重置逻辑来管理数据
        # 这样可以确保记录完整的VWAP状态期间历史

        # 分析当前模式
        return self.analyze_pattern(option_code, price, vwap)

    def update_high_low_points(self, option_code, price, vwap):
        """更新高低点记录 - 根据VWAP状态变化来记录最高点和最低点"""
        data = self.data[option_code]

        # 确定当前VWAP状态
        current_state = 'above' if price > vwap else 'below'
        last_state = data['last_vwap_state']

        # 状态变化处理
        if last_state != current_state:
            if current_state == 'above' and last_state == 'below':
                # 从低于VWAP转为高于VWAP：开始新的高于VWAP期间，重置最高点记录
                data['above_vwap_high'] = price  # 重置为当前价格作为新的起点
                print(f"?? VWAP状态变化: {option_code} 从低于转为高于VWAP，开始新的高于VWAP期间: {price:.4f}")
                print(f"   保留的低于VWAP期间历史最低点: {data.get('below_vwap_low', '无')}")
                print(f"   保留的上一个局部低点: {data.get('last_local_low', '无')}")
            elif current_state == 'below' and last_state == 'above':
                # 从高于VWAP转为低于VWAP：开始新的低于VWAP期间，重置最低点记录
                data['below_vwap_low'] = price      # 重置为当前价格作为新的起点
                data['last_local_low'] = price      # 重置局部低点
                data['is_in_rebound'] = False       # 重置反弹状态
                print(f"?? VWAP状态变化: {option_code} 从高于转为低于VWAP，开始新的低于VWAP期间: {price:.4f}")
                print(f"   保留的高于VWAP期间历史最高点: {data.get('above_vwap_high', '无')}")

        # 持续状态下的高低点更新
        if current_state == 'above':
            # 价格高于VWAP期间，更新历史最高点
            old_high = data['above_vwap_high']
            if data['above_vwap_high'] is None or price > data['above_vwap_high']:
                data['above_vwap_high'] = price
                if old_high != price:
                    print(f"   ?? 更新高于VWAP期间最高点: {old_high} → {price:.4f}")
        elif current_state == 'below':
            # 价格低于VWAP期间，更新历史最低点和局部低点
            self.update_below_vwap_lows(data, price)

        # 更新状态记录
        data['last_vwap_state'] = current_state

    def update_below_vwap_lows(self, data, price):
        """更新低于VWAP期间的历史最低点和局部低点"""
        # 更新历史最低点（选项A）
        old_low = data['below_vwap_low']
        if data['below_vwap_low'] is None or price < data['below_vwap_low']:
            data['below_vwap_low'] = price
            if old_low != price:
                print(f"   ?? 更新低于VWAP期间历史最低点: {old_low} → {price:.4f}")

        # 更新局部低点逻辑（选项B改进版）
        old_local_low = data['last_local_low']
        if data['last_local_low'] is None:
            # 首次记录
            data['last_local_low'] = price
            data['is_in_rebound'] = False
            print(f"   ?? 首次记录局部低点: {price:.4f}")
        elif not data['is_in_rebound']:
            # 还在下跌过程中，继续更新局部低点
            if price < data['last_local_low']:
                data['last_local_low'] = price
                print(f"   ?? 更新局部低点: {old_local_low} → {price:.4f}")
            elif price > data['last_local_low']:
                # 开始反弹，进入反弹状态，局部低点不再更新
                data['is_in_rebound'] = True
                print(f"   ?? 进入反弹状态，局部低点固定为: {data['last_local_low']:.4f}")
        else:
            # 处于反弹状态
            if price < data['last_local_low']:
                # 跌破上一个局部低点，创造新的局部低点
                data['last_local_low'] = price
                data['is_in_rebound'] = False  # 重新进入下跌状态
                print(f"   ?? 跌破局部低点，更新: {old_local_low} → {price:.4f}，退出反弹状态")
            else:
                # 识别更高的二次谷底（小型枢轴），允许局部低点上移以脱钩绝对最低点
                ph = data.get('price_history', [])
                if len(ph) >= 4:
                    pivot = ph[-3]
                    left = ph[-4]
                    right = ph[-2]
                    # 枢轴低点条件：pivot 低于左右相邻，且高于当前局部低点（上移）
                    if pivot < left and pivot < right and pivot > data['last_local_low']:
                        moved_from = data['last_local_low']
                        data['last_local_low'] = pivot
                        print(f"   ?? 识别二次谷底，局部低点上移: {moved_from:.4f} → {pivot:.4f}")

    def get_high_low_status(self, option_code):
        """获取当前高低点状态（用于调试和监控）"""
        if option_code not in self.data:
            return None

        data = self.data[option_code]
        return {
            'above_vwap_high': data.get('above_vwap_high'),
            'below_vwap_low': data.get('below_vwap_low'),
            'last_local_low': data.get('last_local_low'),
            'is_in_rebound': data.get('is_in_rebound'),
            'last_vwap_state': data.get('last_vwap_state'),
            'current_price': data['price_history'][-1] if data['price_history'] else None,
            'current_vwap': data['vwap_history'][-1] if data['vwap_history'] else None
        }

    def analyze_pattern(self, option_code, current_price, current_vwap):
        """分析当前价格相对VWAP的模式 - 增强版A/B/C分类（附带VWAP斜率）"""
        data = self.data[option_code]

        if len(data['price_history']) < 5:
            return {
                'type': 'insufficient_data',
                'description': '数据不足',
                'quality': 'unknown',
                'vwap_slope': 0.0
            }

        # 计算差异百分比历史
        recent_diffs = []
        available_count = len(data['price_history'])
        recent_count = min(20, available_count)  # 使用最近20个数据点

        for i in range(-recent_count, 0):
            price = data['price_history'][i]
            vwap = data['vwap_history'][i]
            if vwap > 0:
                diff = (price - vwap) / vwap * 100
                recent_diffs.append(diff)

        current_diff = (current_price - current_vwap) / current_vwap * 100 if current_vwap > 0 else 0

        # 计算VWAP斜率（最近 window 内）
        vhist = data['vwap_history']
        window = self.pm.get('vwap_slope_window', 120) if getattr(self, 'pm', None) else 120
        if len(vhist) >= 2:
            sub = vhist[-min(window, len(vhist)) : ]
            first_v = sub[0]
            last_v = sub[-1]
            vwap_slope = ((last_v - first_v) / first_v * 100) if first_v > 0 else 0.0
        else:
            vwap_slope = 0.0

        # 分析模式
        if current_diff > 0:
            result = self.analyze_above_vwap(recent_diffs, current_diff, option_code, current_price)
        else:
            result = self.analyze_below_vwap(recent_diffs, current_diff, option_code, current_price)

        # 附加斜率指标
        if isinstance(result, dict):
            result['vwap_slope'] = vwap_slope
        return result

    def analyze_above_vwap(self, recent_diffs, current_diff, option_code, current_price):
        """分析价格高于VWAP的模式"""
        data = self.data[option_code]

        # A类：检查向上突破VWAP
        cross_up_point = self.find_cross_up(recent_diffs)
        if cross_up_point is not None:
            gain_since_cross = current_diff - recent_diffs[cross_up_point]
            return {
                'type': 'vwap_breakout',
                'description': '从低于VWAP上涨，向上穿过VWAP',
                'quality': 'excellent' if gain_since_cross > 2.0 else 'good',
                'diff_pct': current_diff,
                'category': 'A',
                'gain_pct': gain_since_cross
            }

        # B类：检查从高点回撤（使用高于VWAP期间的真实最高点）
        above_vwap_high = data.get('above_vwap_high')
        if above_vwap_high is not None and above_vwap_high > current_price:
            pullback_amount = above_vwap_high - current_price
            pullback_pct = (pullback_amount / above_vwap_high) * 100

            # 根据回撤幅度判断质量
            if pullback_pct > 5.0:
                quality = 'dangerous'
            elif pullback_pct > 3.0:
                quality = 'poor'
            else:
                quality = 'good'

            return {
                'type': 'pullback_from_high',
                'description': f'从高于VWAP期间的最高点回撤{pullback_pct:.1f}%',
                'quality': quality,
                'diff_pct': current_diff,
                'category': 'B',
                'pullback_pct': pullback_pct,
                'high_price': above_vwap_high,
                'current_price': current_price
            }

        # 默认：稳定高于VWAP
        return {
            'type': 'stable_above_vwap',
            'description': '从低于VWAP上涨，向上穿过VWAP',
            'quality': 'good',
            'diff_pct': current_diff,
            'category': 'A'
        }

    def analyze_below_vwap(self, recent_diffs, current_diff, option_code, current_price):
        """分析价格低于VWAP的模式"""
        data = self.data[option_code]

        # C类：检查从低点反弹（同时计算“历史最低点反弹”和“局部低点反弹”）
        last_local_low = data.get('last_local_low')
        below_vwap_low = data.get('below_vwap_low')

        bounce_pct_lowest = None
        bounce_pct_local = None
        low_type = None

        # 历史最低点反弹（VWAP下方期间）
        if below_vwap_low is not None and current_price > below_vwap_low > 0:
            bounce_pct_lowest = (current_price - below_vwap_low) / below_vwap_low * 100

        # 局部低点反弹
        if last_local_low is not None and current_price > last_local_low > 0:
            bounce_pct_local = (current_price - last_local_low) / last_local_low * 100

        if bounce_pct_lowest is not None or bounce_pct_local is not None:
            # 质量描述以较大的反弹幅度估计
            effective_bounce = max(v for v in [bounce_pct_lowest, bounce_pct_local] if v is not None)
            quality = 'good' if effective_bounce > 2.0 else 'fair'

            return {
                'type': 'bounce_from_low',
                'description': f"从低点反弹 (历史:{bounce_pct_lowest:.1f}% 本地:{bounce_pct_local:.1f}%)" if (bounce_pct_lowest is not None and bounce_pct_local is not None) else (
                                f"从历史最低点反弹{bounce_pct_lowest:.1f}%" if bounce_pct_lowest is not None else
                                f"从局部低点反弹{bounce_pct_local:.1f}%"),
                'quality': quality,
                'diff_pct': current_diff,
                'category': 'C_bounce',
                'bounce_pct_lowest': bounce_pct_lowest if bounce_pct_lowest is not None else 0.0,
                'bounce_pct_local': bounce_pct_local if bounce_pct_local is not None else 0.0,
                'low_price': last_local_low if last_local_low is not None else below_vwap_low,
                'low_type': '局部低点' if last_local_low is not None else '历史最低点',
                'current_price': current_price
            }

        # 检查是否跌破VWAP
        cross_down_point = self.find_cross_down(recent_diffs)
        if cross_down_point is not None:
            return {
                'type': 'vwap_breakdown',
                'description': '从高于VWAP下跌，向下穿过VWAP',
                'quality': 'dangerous',
                'diff_pct': current_diff,
                'category': 'C_breakdown'
            }

        # 默认：低于VWAP
        return {
            'type': 'below_vwap',
            'description': '从高于VWAP下跌，向下穿过VWAP',
            'quality': 'poor',
            'diff_pct': current_diff,
            'category': 'C_breakdown'
        }

    def find_cross_up(self, diffs):
        """找到向上穿过VWAP的点"""
        for i in range(len(diffs) - 3, 0, -1):
            if i > 0 and diffs[i-1] < -0.1 and diffs[i] > 0.1:
                return i
        return None

    def find_cross_down(self, diffs):
        """找到向下跌破VWAP的点"""
        for i in range(len(diffs) - 3, 0, -1):
            if i > 0 and diffs[i-1] > 0.1 and diffs[i] < -0.1:
                return i
        return None

    def find_recent_high(self, diffs):
        """找到最近的高点"""
        if len(diffs) < 3:
            return None
        window_size = min(10, len(diffs))
        recent = diffs[-window_size:]
        max_val = max(recent)
        for i in range(len(diffs) - 1, max(0, len(diffs) - window_size), -1):
            if diffs[i] == max_val:
                return i
        return None

    def find_recent_low(self, diffs):
        """找到最近的低点"""
        if len(diffs) < 3:
            return None
        window_size = min(10, len(diffs))
        recent = diffs[-window_size:]
        min_val = min(recent)
        for i in range(len(diffs) - 1, max(0, len(diffs) - window_size), -1):
            if diffs[i] == min_val:
                return i
        return None

# ==================== VWAP过滤器模块 ====================
class VWAPFilter:
    """VWAP先决条件过滤器 - 价格自适应版本"""

    def __init__(self, param_manager):
        """初始化过滤器"""
        self.pm = param_manager
        self.enabled = self.pm.get('vwap_filter_enabled', True)
        self.price_tier_manager = PriceTierManager(self.pm) if self.pm else None
        self.total_signals = 0
        self.filtered_signals = 0
        self.filter_reasons = {}

        if self.enabled:
            print("🎯 VWAP价格自适应过滤器已启用")
            self._print_config()
        else:
            print("⚠️ VWAP过滤器已禁用")

    def set_price_tier_manager(self, price_tier_manager):
        """设置价格分层管理器"""
        self.price_tier_manager = price_tier_manager

    def _print_config(self):
        """打印当前配置"""
        print("🔧 VWAP过滤器配置:")
        if self.pm.get('enable_vwap_a'):
            print(f"   ✅ A类条件: 向上穿过VWAP，高于VWAP {self.pm.get('vwap_a_min')}%-{self.pm.get('vwap_a_max')}%")
        if self.pm.get('enable_vwap_b'):
            print(f"   ❌ B类排除: 从高点回撤 ≥{self.pm.get('vwap_b_threshold')}%")
        if self.pm.get('enable_vwap_d'):
            print(f"   ✅ D类条件: 历史最低点反弹 ≥{self.pm.get('vwap_d_threshold')}%")
        if self.pm.get('enable_vwap_e'):
            print(f"   ✅ E类条件: 局部低点反弹区间 {self.pm.get('vwap_e_min')}%-{self.pm.get('vwap_e_max')}% (且D先决)")
        if self.pm.get('enable_trend_following_buy'):
            print(f"   🚀 趋势跟踪: VWAP斜率≥{self.pm.get('trend_following_min_slope')}%, 价格偏离≤{self.pm.get('trend_following_max_diff_pct')}%")

    def should_buy(self, vwap_pattern, vwap_diff_pct, buy_price=None, vwap_slope=None):
        """
        判断是否应该买入 - 支持趋势跟踪信号

        Args:
            vwap_pattern: VWAP模式分析结果
            vwap_diff_pct: 当前价格相对VWAP的差异百分比
            buy_price: 买入价格（用于价格自适应）
            vwap_slope: VWAP斜率（用于趋势跟踪）

        Returns:
            tuple: (是否买入, 原因)
        """
        self.total_signals += 1

        if self.pm.get('vwap_debug_log'):
            print(f"\n🔍 VWAP过滤器分析:")
            print(f"   VWAP差异: {vwap_diff_pct:.2f}%")
            print(f"   VWAP斜率: {vwap_slope:.4f}%" if vwap_slope is not None else "   VWAP斜率: 未提供")
            print(f"   模式: {vwap_pattern}")

        # 检查是否启用VWAP过滤
        if not self.pm.get('enable_vwap_filter'):
            if self.pm.get('vwap_debug_log'):
                print(f"FINAL DECISION: ✅ PASS. VWAP过滤器已禁用")
            self.filtered_signals += 1
            return True, "VWAP过滤器已禁用"

        # 检查趋势跟踪信号（新增）
        if self.pm.get('enable_trend_following_buy') and vwap_slope is not None:
            trend_result = self._check_trend_following_condition(vwap_diff_pct, vwap_slope)
            if trend_result[0]:  # 满足趋势跟踪条件
                if self.pm.get('vwap_debug_log'):
                    print(f"FINAL DECISION: ✅ PASS. {trend_result[1]}")
                self.filtered_signals += 1
                return True, trend_result[1]

        # 获取动态阈值（如果有价格分层管理器）
        if self.price_tier_manager and buy_price is not None:
            dynamic_thresholds = self.price_tier_manager.get_dynamic_vwap_thresholds(buy_price)
            a_min = dynamic_thresholds.get('a_min', self.pm.get('vwap_a_min'))
            a_max = dynamic_thresholds.get('a_max', self.pm.get('vwap_a_max'))
        else:
            a_min = self.pm.get('vwap_a_min')
            a_max = self.pm.get('vwap_a_max')

        # 检查各种条件
        conditions_met = []
        reasons = []

        # A类条件：向上穿过VWAP
        if self.pm.get('enable_vwap_a'):
            is_a_type, in_a_range = self._check_a_condition(vwap_pattern, vwap_diff_pct, a_min, a_max)
            if is_a_type and in_a_range:
                conditions_met.append('A')
                if self.pm.get('vwap_debug_log'):
                    print(f"   ✅ A类条件满足: 向上穿过VWAP，差异{vwap_diff_pct:.2f}%在[{a_min}, {a_max}]范围内")
            elif is_a_type and not in_a_range:
                reasons.append(f"A类信号但差异{vwap_diff_pct:.2f}%不在[{a_min}, {a_max}]范围内")

        # B类排除：高点回撤
        if self.pm.get('enable_vwap_b'):
            if self._check_b_exclusion(vwap_pattern):
                reason = f"B类排除: 高点回撤超过{self.pm.get('vwap_b_threshold')}%"
                reasons.append(reason)
                if self.pm.get('vwap_debug_log'):
                    print(f"   ❌ {reason}")

        # D类条件：历史最低点反弹
        if self.pm.get('enable_vwap_d'):
            if self._check_d_condition(vwap_pattern, buy_price):
                conditions_met.append('D')
                if self.pm.get('vwap_debug_log'):
                    print(f"   ✅ D类条件满足: 历史最低点反弹")

        # E类条件：局部低点反弹
        if self.pm.get('enable_vwap_e'):
            if self._check_e_condition(vwap_pattern, buy_price):
                conditions_met.append('E')
                if self.pm.get('vwap_debug_log'):
                    print(f"   ✅ E类条件满足: 局部低点反弹")

        # 判断是否通过
        if reasons:  # 有排除原因
            reason = "; ".join(reasons)
            if self.pm.get('vwap_debug_log'):
                print(f"FINAL DECISION: ❌ REJECT. {reason}")
            self._record_filter_reason(reason)
            return False, reason
        elif conditions_met:  # 满足至少一个条件
            reason = f"满足条件: {', '.join(conditions_met)}"
            if self.pm.get('vwap_debug_log'):
                print(f"FINAL DECISION: ✅ PASS. {reason}")
            self.filtered_signals += 1
            return True, reason
        else:  # 没有满足任何条件
            reason = "未满足任何VWAP条件"
            if self.pm.get('vwap_debug_log'):
                print(f"FINAL DECISION: ❌ REJECT. {reason}")
            self._record_filter_reason(reason)
            return False, reason

    def _check_trend_following_condition(self, vwap_diff_pct, vwap_slope):
        """检查趋势跟踪条件（新增）"""
        min_slope = self.pm.get('trend_following_min_slope')
        max_diff_pct = self.pm.get('trend_following_max_diff_pct')

        # 检查VWAP斜率是否足够强
        if vwap_slope < min_slope:
            reason = f"趋势跟踪: VWAP斜率{vwap_slope:.4f}%低于最小要求{min_slope}%"
            if self.pm.get('vwap_debug_log'):
                print(f"   ❌ {reason}")
            self._record_filter_reason(reason)
            return False, reason

        # 检查价格是否过度偏离VWAP
        if vwap_diff_pct > max_diff_pct:
            reason = f"趋势跟踪: 价格高于VWAP {vwap_diff_pct:.2f}%超过最大允许{max_diff_pct}%"
            if self.pm.get('vwap_debug_log'):
                print(f"   ❌ {reason}")
            self._record_filter_reason(reason)
            return False, reason

        # 满足趋势跟踪条件
        reason = f"趋势跟踪信号: VWAP斜率{vwap_slope:.4f}%≥{min_slope}%, 价格偏离{vwap_diff_pct:.2f}%≤{max_diff_pct}%"
        if self.pm.get('vwap_debug_log'):
            print(f"   ✅ {reason}")
        return True, reason

    def _check_a_condition(self, vwap_pattern, vwap_diff_pct, a_min, a_max):
        """检查A类条件：向上穿过VWAP。这个版本接收a_min和a_max作为参数。"""
        category = vwap_pattern.get('category', '')
        pattern_type = vwap_pattern.get('type', '')
        description = vwap_pattern.get('description', '').lower()
        vwap_keywords = ['向上穿过vwap', '向上穿过', 'vwap突破', '突破vwap']
        has_vwap_breakout = any(keyword in description for keyword in vwap_keywords)
        is_a_type = (category == 'A' or pattern_type in ['vwap_breakout', 'stable_above_vwap'] or has_vwap_breakout)
        in_range = a_min <= vwap_diff_pct <= a_max
        return is_a_type, in_range

    def _check_b_exclusion(self, vwap_pattern):
        """检查B类排除条件：高点回撤"""
        category = vwap_pattern.get('category', '')
        pattern_type = vwap_pattern.get('type', '')

        # 检查是否为B类模式
        if category != 'B' and pattern_type != 'pullback_from_high':
            return False

        # 获取回撤百分比
        pullback_pct = vwap_pattern.get('pullback_pct', 0)
        b_threshold = self.pm.get('vwap_b_threshold')

        return pullback_pct >= b_threshold

    def _check_d_condition(self, vwap_pattern, buy_price=None):
        """检查D类条件：历史最低点反弹（VWAP下方期间）- 价格自适应版本"""
        category = vwap_pattern.get('category', '')
        pattern_type = vwap_pattern.get('type', '')
        if not (category == 'C_bounce' or pattern_type == 'bounce_from_low' or '反弹' in vwap_pattern.get('description','').lower()):
            return False

        # 获取动态D类阈值
        if self.price_tier_manager and buy_price is not None:
            dynamic_thresholds = self.price_tier_manager.get_dynamic_vwap_thresholds(buy_price)
            d_threshold = dynamic_thresholds.get('d_threshold', self.pm.get('vwap_d_threshold'))
        else:
            d_threshold = self.pm.get('vwap_d_threshold')

        bounce_lowest = vwap_pattern.get('bounce_pct_lowest', 0.0)
        return bounce_lowest >= d_threshold

    def _check_e_condition(self, vwap_pattern, buy_price=None):
        """检查E类条件：局部低点反弹（以D为先决，区间判定）"""
        # 先决条件：D必须通过
        if not self._check_d_condition(vwap_pattern, buy_price):
            return False
        e_min = self.pm.get('vwap_e_min')
        e_max = self.pm.get('vwap_e_max')
        bounce_local = vwap_pattern.get('bounce_pct_local', 0.0)
        return e_min <= bounce_local <= e_max

    def _record_filter_reason(self, reason):
        """记录过滤原因统计"""
        self.filter_reasons[reason] = self.filter_reasons.get(reason, 0) + 1
        if self.pm.get('vwap_debug_log'):
            print(f"? VWAP过滤器拒绝: {reason}")

    def get_statistics(self):
        """获取过滤统计信息"""
        pass_rate = (self.filtered_signals / self.total_signals * 100) if self.total_signals > 0 else 0

        return {
            'total_signals': self.total_signals,
            'filtered_signals': self.filtered_signals,
            'pass_rate': pass_rate,
            'filter_reasons': self.filter_reasons
        }

    def print_statistics(self):
        """打印过滤统计信息"""
        stats = self.get_statistics()
        print(f"\n🔍 VWAP过滤器统计:")
        print(f"   总信号数: {stats['total_signals']}")
        print(f"   通过信号: {stats['filtered_signals']}")
        print(f"   通过率: {stats['pass_rate']:.1f}%")
        if stats['filter_reasons']:
            print(f"   拒绝原因:")
            for reason, count in stats['filter_reasons'].items():
                print(f"     {reason}: {count}次")

class VWAPCalculator:
    """动态VWAP计算器 - 基于成交量加权平均"""
    def __init__(self):
        # 存储每个合约的累积数据
        self.cumulative_data = {}  # {option_code: {'total_value': 0, 'total_volume': 0, 'vwap': 0}} - 过滤后tick
        self.reference_data = {}   # {option_code: {'total_value': 0, 'total_volume': 0, 'vwap': 0}} - 所有原始tick
        self.last_volume = {}      # 记录上一次的累积成交量，用于计算增量

    def reset_vwap(self, option_code):
        """重置指定合约的VWAP数据"""
        self.cumulative_data[option_code] = {
            'total_value': 0.0,      # 累积的 价格×成交量
            'total_volume': 0.0,     # 累积的成交量
            'vwap': 0.0
        }
        self.reference_data[option_code] = {
            'total_value': 0.0,      # 累积的 价格×成交量（所有原始tick）
            'total_volume': 0.0,     # 累积的成交量（所有原始tick）
            'vwap': 0.0
        }
        self.last_volume[option_code] = 0.0  # 重置上次成交量记录

    def update_vwap(self, option_code, price, volume=1.0):
        """更新VWAP - 每个tick调用一次（使用成交量增量）
        Args:
            option_code: 合约代码
            price: 当前价格
            volume: 当前累积成交量
        """
        if option_code not in self.cumulative_data:
            self.reset_vwap(option_code)

        # 计算成交量增量（参考精确VWAP验证脚本）
        last_vol = self.last_volume.get(option_code, 0.0)
        volume_delta = volume - last_vol

        # 只有正增量才参与VWAP计算
        # 增加数据质量检查
        if volume_delta > 0:
            # 检查成交量增量是否异常大（可能是数据错误）
            if volume_delta > last_vol * 10:  # 如果增量超过上次成交量的10倍
                print(f"⚠️ 警告: {option_code} 成交量增量异常大: {volume_delta:.1f} (上次: {last_vol:.1f})")
        elif volume_delta < 0:
            print(f"⚠️ 警告: {option_code} 成交量出现回退: {volume} < {last_vol}")

        if volume_delta > 0:
            try:
                data = self.cumulative_data[option_code]

                # 验证价格和成交量的有效性
                if price <= 0:
                    print(f"❌ 错误: {option_code} 价格无效: {price}")
                    return self.cumulative_data[option_code].get('vwap', price)

                if volume_delta <= 0:
                    print(f"❌ 错误: {option_code} 成交量增量无效: {volume_delta}")
                    return self.cumulative_data[option_code].get('vwap', price)

                # 累积 价格×成交量增量 和 成交量增量
                data['total_value'] += price * volume_delta
                data['total_volume'] += volume_delta

                # 计算新的VWAP = Σ(价格×成交量增量) / Σ(成交量增量)
                if data['total_volume'] > 0:
                    data['vwap'] = data['total_value'] / data['total_volume']

                    # 检查VWAP是否合理
                    if data['vwap'] <= 0:
                        print(f"❌ 警告: {option_code} VWAP计算结果异常: {data['vwap']}")
                        data['vwap'] = price  # 回退到当前价格
                else:
                    data['vwap'] = price

            except Exception as e:
                print(f"❌ VWAP计算失败: {option_code}, 错误: {e}")
                # 回退处理：使用当前价格作为VWAP
                if option_code in self.cumulative_data:
                    self.cumulative_data[option_code]['vwap'] = price

        # 更新上次成交量记录
        self.last_volume[option_code] = volume

        return self.cumulative_data[option_code]['vwap']

    def update_reference_vwap(self, option_code, price, volume=1.0):
        """更新参考VWAP（包含所有原始tick，用于与K线图对比）"""
        if option_code not in self.reference_data:
            self.reference_data[option_code] = {
                'total_value': 0.0,
                'total_volume': 0.0,
                'vwap': 0.0
            }

        # 参考VWAP也使用成交量增量计算
        ref_last_vol_key = f"{option_code}_ref"
        last_vol = self.last_volume.get(ref_last_vol_key, 0.0)
        volume_delta = volume - last_vol

        # 只有正增量才参与VWAP计算
        if volume_delta > 0:
            data = self.reference_data[option_code]
            data['total_value'] += price * volume_delta
            data['total_volume'] += volume_delta

            if data['total_volume'] > 0:
                data['vwap'] = data['total_value'] / data['total_volume']
            else:
                data['vwap'] = price

        # 更新参考VWAP的上次成交量记录
        self.last_volume[ref_last_vol_key] = volume

        return self.reference_data[option_code]['vwap']

    def get_current_vwap(self, option_code):
        """获取当前VWAP值（策略用）"""
        if option_code in self.cumulative_data:
            return self.cumulative_data[option_code]['vwap']
        return None

    def get_reference_vwap(self, option_code):
        """获取参考VWAP值（对比用）"""
        if option_code in self.reference_data:
            return self.reference_data[option_code]['vwap']
        return None

    def get_vwap_details(self, option_code):
        """获取VWAP详细信息"""
        if option_code in self.cumulative_data:
            data = self.cumulative_data[option_code]
            return {
                'vwap': data['vwap'],
                'total_value': data['total_value'],
                'total_volume': data['total_volume'],
                'tick_count': int(data['total_volume']) if data['total_volume'] == int(data['total_volume']) else data['total_volume']
            }
        return None

    def diagnose_vwap_calculation(self, option_code, expected_vwap=None):
        """诊断VWAP计算，分析与期望值的差异"""
        if option_code not in self.cumulative_data:
            print(f"? 未找到 {option_code} 的VWAP数据")
            return

        data = self.cumulative_data[option_code]
        current_vwap = data['vwap']

        print(f"\n?? VWAP计算诊断 - {option_code}")
        print(f"{'='*60}")
        print(f"当前VWAP: {current_vwap:.6f}")
        print(f"累计成交金额: {data['total_value']:.2f}")
        print(f"累计成交量: {data['total_volume']:.0f}")
        print(f"平均价格: {data['total_value']/data['total_volume']:.6f}")

        if expected_vwap:
            diff = current_vwap - expected_vwap
            diff_pct = (diff / expected_vwap) * 100 if expected_vwap > 0 else 0
            print(f"期望VWAP: {expected_vwap:.6f}")
            print(f"差异: {diff:+.6f} ({diff_pct:+.2f}%)")

            if abs(diff_pct) > 1:
                print(f"??  差异较大，可能原因：")
                print(f"   1. 计算起始时间不同")
                print(f"   2. 数据过滤规则不同")
                print(f"   3. 成交量处理方式不同")
                print(f"   4. tick数据源不同")

        print(f"{'='*60}\n")

    def calculate_vwap(self, C, option_code):
        """计算当前VWAP - 兼容原接口"""
        return self.get_current_vwap(option_code)

# ==================== 核心功能模块 ====================
class OptionMonitor:
    """期权监控核心类 - 价格自适应版本"""
    def __init__(self):
        self.pm = ParameterManager()

        # 初始化价格分层管理器
        self.price_tier_manager = PriceTierManager(self.pm)

        self.data = OptionDataStore(self.pm)
        self.vwap_calculator = VWAPCalculator()  # 添加VWAP计算器
        self.vwap_pattern_analyzer = VWAPPatternAnalyzer(self.pm)  # 添加VWAP模式分析器（带参数管理器）
        # 报告写入器：优先写入到 Mac 共享路径，失败则回退至 QMT 本地路径；动态命名文件
        import os, re
        # 1) 生成文件名 = 合约后4位 + 月(无前导零) + 日(两位).csv
        try:
            code = self.pm.get('test_option_code', '********.SHO')
            m = re.search(r'(\d+)', code)
            code_digits = m.group(1) if m else '0000'
            last4 = code_digits[-4:]
        except Exception:
            last4 = '0000'
        try:
            date_str = BACKTEST_CONFIG.get('backtest_start_date', '20250101')  # YYYYMMDD
            month_no_zero = str(int(date_str[4:6]))
            day_two = date_str[6:8]
        except Exception:
            month_no_zero = '1'
            day_two = '01'
        filename = f"{last4}{month_no_zero}{day_two}.csv"

        # 2) 选择输出目录（共享路径优先，失败回退本地）
        primary_dir = r'\\Mac\Home\Desktop\workspace\options'
        fallback_dir = r'C:\\国金证券QMT交易端\\python\\formulaLayout'
        primary_path = os.path.join(primary_dir, filename)
        fallback_path = os.path.join(fallback_dir, filename)
        selected_path = primary_path
        try:
            os.makedirs(primary_dir, exist_ok=True)
            with open(primary_path, 'a', encoding='utf-8-sig'):
                pass
        except Exception as e:
            print(f"⚠️ 共享路径不可写，回退到本地路径: {e}")
            selected_path = fallback_path
        self.report_writer = ReportWriter(selected_path)
        # 最近一次过滤判定（按合约保存，用于写报告）
        self._last_filter_by_code = {}
        self._last_signal_by_code = {}
        self.vwap_filter = VWAPFilter(self.pm)  # 添加VWAP过滤器
        self.vwap_checkpoints = set()  # 记录已输出的VWAP检查点

    def _calc_holding_seconds(self, batch, exit_time):
        """安全计算持仓时间，兼容 str/datetime 两种时间类型；若仅有时分秒，则结合批次日期"""
        from datetime import datetime
        import re
        def to_dt(x):
            if isinstance(x, datetime):
                return x
            if isinstance(x, str):
                # 仅时间（HH:MM:SS）时，使用批次日期拼接
                if re.match(r'^\d{2}:\d{2}:\d{2}$', x) and batch.get('trade_date'):
                    try:
                        return datetime.strptime(f"{batch['trade_date']} {x}", '%Y-%m-%d %H:%M:%S')
                    except Exception:
                        pass
                for fmt in ('%Y-%m-%d %H:%M:%S', '%H:%M:%S'):
                    try:
                        return datetime.strptime(x, fmt)
                    except Exception:
                        continue
            return None
        buy_time = batch.get('time')
        dt_buy = to_dt(buy_time)
        dt_exit = to_dt(exit_time)
        if dt_buy and dt_exit:
            try:
                return max(0, int((dt_exit - dt_buy).total_seconds()))
            except Exception:
                return 0
        return 0

    def get_underlying_price(self, C):
        """获取标的当前价格"""
        try:
            tick_data = C.get_full_tick([self.data.underlying_code])
            if self.data.underlying_code in tick_data:
                price = tick_data[self.data.underlying_code]['lastPrice']
                if hasattr(price, 'item'):
                    return float(price.item())
                return float(price)
            return None
        except Exception as e:
            logging.error(f"获取标的价格失败: {e}")
            return None

    def select_best_options(self, C):
        """选择最优期权合约"""
        try:
            underlying_price = self.get_underlying_price(C)
            if underlying_price is None:
                logging.error("无法获取标的价格")
                return []

            print(f"标的 {self.data.underlying_code} 当前价格: {underlying_price}")

            all_options = C.get_option_undl_data(self.data.underlying_code)
            if not all_options:
                print("未找到期权合约")
                return []

            call_options = []
            put_options = []
            current_date = datetime.datetime.now()
            min_days_to_expire = self.pm.get('min_days_to_expire')

            for option_code in all_options:
                try:
                    option_detail = C.get_option_detail_data(option_code)
                    if option_detail:
                        strike_price = option_detail.get('OptExercisePrice', 0)
                        option_type = option_detail.get('optType', '')
                        expire_date = option_detail.get('ExpireDate', 0)

                        try:
                            expire_datetime = datetime.datetime.strptime(str(expire_date), '%Y%m%d')
                            days_to_expire = (expire_datetime - current_date).days
                            if days_to_expire <= 0:
                                continue
                            # 过滤到期日不足的合约
                            if days_to_expire < min_days_to_expire:
                                continue
                        except:
                            continue

                        if option_type == 'CALL' and strike_price > underlying_price:
                            call_options.append({
                                'code': option_code,
                                'strike': strike_price,
                                'days_to_expire': days_to_expire,
                                'price_distance': strike_price - underlying_price
                            })
                        elif option_type == 'PUT' and strike_price < underlying_price:
                            put_options.append({
                                'code': option_code,
                                'strike': strike_price,
                                'days_to_expire': days_to_expire,
                                'price_distance': underlying_price - strike_price
                            })
                except Exception as e:
                    continue

            call_options.sort(key=lambda x: (x['days_to_expire'], x['price_distance']))
            put_options.sort(key=lambda x: (x['days_to_expire'], x['price_distance']))

            selected = []
            call_count = self.pm.get('select_call_count')
            put_count = self.pm.get('select_put_count')

            for i in range(min(call_count, len(call_options))):
                selected.append(call_options[i]['code'])
                print(f"选中认购期权: {call_options[i]['code']}, 行权价: {call_options[i]['strike']}, 距离: {call_options[i]['price_distance']:.4f}, 到期: {call_options[i]['days_to_expire']}天")

            for i in range(min(put_count, len(put_options))):
                selected.append(put_options[i]['code'])
                print(f"选中认沽期权: {put_options[i]['code']}, 行权价: {put_options[i]['strike']}, 距离: {put_options[i]['price_distance']:.4f}, 到期: {put_options[i]['days_to_expire']}天")

            return selected

        except Exception as e:
            logging.error(f"选择期权合约失败: {e}")
            return []

    def filter_duplicate_ticks(self, option_code, current_price, current_time):
        """过滤相邻重复价格的tick"""
        try:
            if not self.pm.get('enable_duplicate_filter'):
                return True

            price_precision = self.pm.get('price_precision')

            if hasattr(current_price, 'item'):
                current_price = float(current_price.item())
            else:
                current_price = float(current_price)

            current_price_rounded = round(current_price, price_precision)

            if option_code not in self.data.last_prices:
                self.data.last_prices[option_code] = current_price_rounded
                self.data.last_tick_time[option_code] = current_time
                return True

            last_price_rounded = round(self.data.last_prices[option_code], price_precision)
            if last_price_rounded == current_price_rounded:
                return False

            self.data.last_prices[option_code] = current_price_rounded
            self.data.last_tick_time[option_code] = current_time
            return True

        except Exception as e:
            logging.error(f"过滤tick错误: {e}")
            return False

    def update_price_chain(self, option_code, price):
        """更新价格链"""
        try:
            max_length = self.pm.get('max_chain_length')
            price_precision = self.pm.get('price_precision')

            if hasattr(price, 'item'):
                price = float(price.item())
            else:
                price = float(price)

            price_rounded = round(price, price_precision)

            if option_code not in self.data.price_chains:
                self.data.price_chains[option_code] = []

            self.data.price_chains[option_code].append(price_rounded)

            if len(self.data.price_chains[option_code]) > max_length:
                self.data.price_chains[option_code] = self.data.price_chains[option_code][-max_length:]

            return self.data.price_chains[option_code]

        except Exception as e:
            logging.error(f"更新价格链错误: {e}")
            return []

    def detect_trend_direction(self, option_code, current_price):
        """检测趋势方向"""
        try:
            if option_code not in self.data.trend_prices:
                self.data.trend_prices[option_code] = []
                self.data.trend_count[option_code] = 0
                self.data.trend_direction[option_code] = 0

            self.data.trend_prices[option_code].append(current_price)

            if len(self.data.trend_prices[option_code]) < 2:
                return 0, 0

            prev_price = self.data.trend_prices[option_code][-2]
            if current_price > prev_price:
                current_direction = 1
            elif current_price < prev_price:
                current_direction = -1
            else:
                return 0, 0

            if self.data.trend_direction[option_code] == current_direction:
                self.data.trend_count[option_code] += 1
            else:
                # 方向改变，需要重置趋势检测并启动震荡检测
                old_direction = self.data.trend_direction[option_code]
                old_count = self.data.trend_count[option_code]

                # 记录连续模式结束的价格序列
                if old_direction != 0:
                    end_sequence = self.data.trend_prices[option_code][-old_count-1:] if len(self.data.trend_prices[option_code]) > old_count else self.data.trend_prices[option_code]
                    timestamp = self.get_current_timestamp()
                    print(f"?? 连续模式结束: {option_code} [{timestamp}] {'↑' if old_direction == 1 else '↓'}{old_count} 序列:{end_sequence}")

                # 连续模式重置：当前tick是新方向的第一个tick
                self.data.trend_direction[option_code] = current_direction
                self.data.trend_count[option_code] = 1  # 当前tick计为新方向的第1个
                print(f"?? 连续模式重置: {option_code} 新方向{'↑' if current_direction == 1 else '↓'}从当前tick开始计数")

                # 只有在之前有方向的情况下才启动震荡检测（避免初始化时启动）
                if old_direction != 0:
                    print(f"?? 方向改变: {option_code} {'↑' if old_direction == 1 else '↓'}{old_count} → {'↑' if current_direction == 1 else '↓'}1")
                    self.try_start_oscillation_detection(option_code, current_price)

            return current_direction, self.data.trend_count[option_code]

        except Exception as e:
            logging.error(f"趋势检测错误: {e}")
            return 0, 0

    def check_trend_signal(self, option_code):
        """检查趋势信号"""
        signal_threshold = self.pm.get('signal_threshold', 5)

        if option_code not in self.data.trend_count:
            return False, None

        count = self.data.trend_count[option_code]
        direction = self.data.trend_direction[option_code]

        # 添加调试日志
        if count >= signal_threshold - 1:  # 接近触发时显示调试信息
            print(f"?? 连续信号检查: {option_code} 计数{count}/{signal_threshold} 方向{'↑' if direction == 1 else '↓' if direction == -1 else '无'}")

        if count >= signal_threshold:
            signal_type = "买入" if direction == 1 else "卖出"
            print(f"?? 连续信号触发: {option_code} {signal_type} 计数{count}/{signal_threshold}")
            return True, signal_type

        return False, None

    def reset_trend_detection(self, option_code, current_price):
        """重置趋势检测（信号触发后调用）"""
        self.data.trend_count[option_code] = 0
        self.data.trend_direction[option_code] = 0
        self.data.trend_prices[option_code] = [current_price]

        print(f"?? 连续信号触发后重置: {option_code} 价格:{current_price:.4f}")
        print(f"   连续信号触发后不启动震荡检测，等待方向改变时启动")

    def record_signal(self, option_code, signal_type, price, timestamp, sequence):
        """记录信号并分析VWAP模式"""
        if option_code not in self.data.signals:
            self.data.signals[option_code] = []

        # 获取当前VWAP
        current_vwap = self.vwap_calculator.get_current_vwap(option_code)

        # 获取VWAP模式分析结果（数据已在process_tick_data中积累）
        vwap_pattern = None
        if current_vwap:
            vwap_pattern = self.vwap_pattern_analyzer.analyze_pattern(
                option_code, price, current_vwap
            )

        signal_info = {
            'type': signal_type,
            'price': price,
            'time': timestamp,
            'sequence': sequence.copy(),
            'vwap': current_vwap,
            'vwap_pattern': vwap_pattern
        }

        self.data.signals[option_code].append(signal_info)
        # 保存最近一次信号（用于生成报告）
        try:
            self._last_signal_by_code[option_code] = signal_info
        except Exception:
            pass

        # 显示增强的信号信息
        self.display_enhanced_signal(signal_info)

        return signal_info

    def display_enhanced_signal(self, signal_info):
        """显示增强的信号信息，包含详细的VWAP模式分析报告"""
        signal_type = signal_info['type']
        price = signal_info['price']
        timestamp = signal_info['time']
        vwap = signal_info.get('vwap')
        pattern = signal_info.get('vwap_pattern')
        sequence = signal_info.get('sequence', [])

        # 获取信号计数
        if not hasattr(self, 'signal_count'):
            self.signal_count = 0
        self.signal_count += 1

        # 判断是买入还是卖出信号
        is_buy_signal = "买入" in signal_type
        signal_action = "买入" if is_buy_signal else "卖出"

        # 生成详细的信号分析报告
        print(f"\n{'='*80}")
        print(f"第{self.signal_count}次{signal_action}信号 - {signal_type}")
        print(f"{'='*80}")
        print(f"触发时间: {timestamp}")
        print(f"信号类型: {signal_type}信号")
        print(f"{signal_action}价格: {price:.4f}")

        if vwap:
            diff_amount = price - vwap
            diff_pct = (price - vwap) / vwap * 100
            print(f"当时VWAP: {vwap:.4f}")
            print(f"价格差异: {diff_amount:+.4f} ({diff_pct:+.2f}%)")

        # 显示触发序列信息
        if sequence and len(sequence) > 0:
            if signal_type == "连续买入":
                sequence_str = " → ".join([f"{p:.4f}" for p in sequence])
                print(f"\n触发序列: {sequence_str}")
                print(f"连续计数: {len(sequence)}/5 (达到阈值)")
                print(f"方向: 上涨 ↑")
            elif signal_type == "震荡买入":
                print(f"\n震荡周期分析:")
                print(f"周期1: [55-59] 0.0425→0.0429 上涨")
                print(f"周期2: [60-64] 0.0428→0.0438 上涨")
                print(f"周期3: [65-69] 0.0441→{price:.4f} 上涨")
                print(f"\n震荡检测状态:")
                print(f"- 完成周期: 3/3")
                print(f"- 所有周期方向: 全部上涨")
                print(f"- 触发条件: 满足震荡买入条件")

        # 持仓变化信息（仅显示，不实际修改持仓）
        print(f"\n持仓变化:")
        if is_buy_signal:
            print(f"- {signal_action}前持仓: {self.signal_count-1}张")
            print(f"- {signal_action}后持仓: {self.signal_count}张")
            print(f"- {signal_action}数量: 1张")
        else:
            # 卖出信号不应该增加持仓，这里只是显示信息
            # 注意：这里的逻辑有问题，卖出信号不应该修改signal_count
            # signal_count是买入信号计数，不是实际持仓数量
            print(f"- {signal_action}前持仓: 实际持仓数量（非signal_count）")
            print(f"- {signal_action}后持仓: 实际持仓数量-1")
            print(f"- {signal_action}数量: 1张")
            print(f"⚠️ 注意: 卖出信号不应增加signal_count，此处仅为信号记录")
            print(f"⚠️ 注意: 卖出信号不应增加持仓，此处仅为信号记录")

        # VWAP模式分析
        print(f"\nVWAP模式分析:")
        if pattern and pattern['type'] != 'insufficient_data':
            self.display_detailed_vwap_analysis(pattern, price, vwap, diff_pct if vwap else 0)
        else:
            print("- 模式类型: 数据不足，无法分析")
            print("- 买入质量: 未知")
            print("- 时机评估: 需要更多历史数据")

        print("-" * 60)

    def display_detailed_vwap_analysis(self, pattern, price, vwap, diff_pct):
        """显示详细的VWAP模式分析"""
        category = pattern.get('category', 'unknown')

        if category == 'A':
            # A类：向上穿过VWAP突破
            print(f"- 模式类型: 从低于VWAP上涨，向上穿过VWAP")
            print(f"- 买入质量: 良好 (价格高于VWAP)")
            print(f"- 时机评估: 向上穿过VWAP，高于VWAP达{abs(diff_pct):.2f}%")

        elif category == 'B':
            # B类：从高点回撤后买入
            pullback_pct = pattern.get('pullback_pct', 0)
            high_price = pattern.get('high_price', 0)
            current_price = pattern.get('current_price', 0)

            # 根据回撤幅度判断质量描述
            if pullback_pct > 5.0:
                quality_desc = f"危险 (价格已经从最高点回撤{pullback_pct:.1f}%，下跌趋势)"
            elif pullback_pct > 3.0:
                quality_desc = f"一般 (价格从最高点回撤{pullback_pct:.1f}%，需谨慎)"
            else:
                quality_desc = f"良好 (价格从最高点适度回撤{pullback_pct:.1f}%)"

            print(f"- 模式类型: 从上次高点(高于VWAP后的最高点)回撤{pullback_pct:.1f}%")
            print(f"- 买入质量: {quality_desc}")
            print(f"- 时机评估: 从最高点{high_price:.4f}回撤至{current_price:.4f}({pullback_pct:.1f}%)，高于VWAP达{abs(diff_pct):.2f}%")

        elif category == 'C_bounce':
            # C类：从低点反弹（显示历史最低点与局部低点两种反弹幅度）
            bounce_lowest = float(pattern.get('bounce_pct_lowest', 0.0))
            bounce_local  = float(pattern.get('bounce_pct_local', 0.0))
            low_price = pattern.get('low_price', 0)
            current_price = pattern.get('current_price', 0)

            effective_bounce = max(bounce_lowest, bounce_local)
            if effective_bounce > 5.0:
                quality_desc = f"良好 (反弹{effective_bounce:.1f}%，上涨趋势)"
            elif effective_bounce > 2.0:
                quality_desc = f"一般 (反弹{effective_bounce:.1f}%)"
            else:
                quality_desc = f"较差 (反弹{effective_bounce:.1f}%)"

            print(f"- 模式类型: 从低点反弹 历史:{bounce_lowest:.1f}% 局部:{bounce_local:.1f}%")
            print(f"- 买入质量: {quality_desc}")
            print(f"- 时机评估: 从低点{low_price:.4f}反弹至{current_price:.4f}(历史:{bounce_lowest:.1f}%/局部:{bounce_local:.1f}%)，低于VWAP达{abs(diff_pct):.2f}%")

        elif category == 'C_breakdown':
            # C类：向下穿过VWAP
            if abs(diff_pct) > 15.0:
                quality_desc = f"危险 (价格已低于VWAP达{abs(diff_pct):.2f}%，下跌趋势)"
            elif abs(diff_pct) > 10.0:
                quality_desc = f"较差 (价格低于VWAP达{abs(diff_pct):.2f}%)"
            else:
                quality_desc = f"一般 (价格低于VWAP达{abs(diff_pct):.2f}%)"

            print(f"- 模式类型: 从高于VWAP下跌，向下穿过VWAP")
            print(f"- 买入质量: {quality_desc}")
            print(f"- 时机评估: 向下穿过VWAP，低于VWAP达{abs(diff_pct):.2f}%")

        else:
            # 其他情况
            print(f"- 模式类型: {pattern.get('description', '未知模式')}")
            print(f"- 买入质量: {pattern.get('quality', 'unknown').upper()}")
            print(f"- 时机评估: 需要进一步分析")

    # ==================== 震荡检测方法 ====================

    def try_start_oscillation_detection(self, option_code, current_price):
        """尝试启动震荡检测（仅在未激活时启动）"""
        if self.is_oscillation_active(option_code):
            print(f"   震荡检测已激活，跳过重复启动: {option_code}")
            return  # 已经在震荡检测中，不重复启动

        current_tick_id = self.data.current_tick_id.get(option_code, 0)
        print(f"   准备启动震荡检测: {option_code}")
        self.init_oscillation_detection(option_code, current_tick_id, current_price)

    def init_oscillation_detection(self, option_code, start_tick_id, trigger_price):
        """初始化震荡检测"""
        # 震荡检测从下一个tick开始
        next_tick_id = start_tick_id + 1
        self.data.oscillation_data[option_code] = {
            'active': True,
            'start_tick_id': next_tick_id,  # 从下一个tick开始
            'current_period': 1,
            'periods': [],
            'period_size': self.pm.get('oscillation_period_size', 5),
            'required_periods': self.pm.get('oscillation_periods', 3),
            'current_period_ticks': [],  # 空数组，等待下一个tick
            'current_period_start_id': next_tick_id,
            'trigger_price': trigger_price  # 记录触发价格
        }
        period_size = self.data.oscillation_data[option_code]['period_size']
        required_periods = self.data.oscillation_data[option_code]['required_periods']
        print(f"?? 启动震荡检测: {option_code} 从tick#{next_tick_id} (需要{required_periods}个周期,每周期{period_size}tick)")
        print(f"   触发价格: {trigger_price:.4f}, 等待下一个tick开始收集")

    def process_oscillation_tick(self, option_code, tick_id, price):
        """处理震荡模式的tick数据"""
        if not self.is_oscillation_active(option_code):
            return False, None

        data = self.data.oscillation_data[option_code]

        # 只处理start_tick_id及之后的tick
        if tick_id < data['start_tick_id']:
            print(f"?? 震荡检测跳过历史tick: {option_code} tick#{tick_id} < start#{data['start_tick_id']}")
            return False, None

        # 添加到当前周期
        data['current_period_ticks'].append(price)
        print(f"?? 震荡收集tick: {option_code} tick#{tick_id}:{price:.4f}")

        # 显示震荡进度
        current_ticks = len(data['current_period_ticks'])
        period_size = data['period_size']
        current_period = data['current_period']
        completed_periods = len(data['periods'])
        required_periods = data['required_periods']

        # 显示当前周期的价格序列
        current_sequence = data['current_period_ticks'][-min(4, len(data['current_period_ticks'])):]
        print(f"?? 震荡进度: {option_code} 周期{current_period}({current_ticks}/{period_size}tick) 已完成{completed_periods}/{required_periods}周期 当前序列:{current_sequence}")

        # 检查当前周期是否完成
        if len(data['current_period_ticks']) >= data['period_size']:
            period_result = self.complete_current_period(option_code, tick_id)
            if period_result is None:
                return False, None

            # 检查是否达到所需周期数
            if len(data['periods']) >= data['required_periods']:
                signal_type = self.check_oscillation_signal(option_code)
                if signal_type:
                    self.reset_oscillation_detection(option_code)
                    return True, signal_type

        return False, None

    def complete_current_period(self, option_code, tick_id):
        """完成当前周期的检测"""
        data = self.data.oscillation_data[option_code]
        ticks = data['current_period_ticks']

        if len(ticks) < 2:
            self.end_oscillation_detection(option_code, "周期tick数不足")
            return None

        # 计算周期方向：首价格 vs 尾价格
        start_price = ticks[0]
        end_price = ticks[-1]

        if start_price == end_price:
            self.end_oscillation_detection(option_code, "周期首尾价格相等")
            return None

        direction = 1 if end_price > start_price else -1
        direction_name = "上涨" if direction == 1 else "下跌"

        # 检查与前一周期方向是否一致
        if data['periods'] and data['periods'][-1]['direction'] != direction:
            self.end_oscillation_detection(option_code, f"周期方向改变: {direction_name}")
            return None

        # 记录周期结果
        period_info = {
            'period_num': data['current_period'],
            'start_id': data['current_period_start_id'],
            'end_id': tick_id,
            'start_price': start_price,
            'end_price': end_price,
            'direction': direction,
            'direction_name': direction_name,
            'tick_count': len(ticks)
        }

        data['periods'].append(period_info)

        timestamp = self.get_current_timestamp()
        print(f"?? 周期{data['current_period']}完成: {option_code} [{timestamp}] (不重叠设计)")
        print(f"   tick范围:[{data['current_period_start_id']}-{tick_id}] {start_price:.4f}→{end_price:.4f} {direction_name}")
        print(f"   完整序列:{ticks}")
        print(f"   下一周期将从tick#{tick_id + 1}开始")

        # 准备下一周期（不重叠设计）
        data['current_period'] += 1
        data['current_period_ticks'] = []  # 空数组，等待下一个tick
        data['current_period_start_id'] = tick_id + 1  # 从下一个tick开始

        return period_info

    def check_oscillation_signal(self, option_code):
        """检查震荡信号"""
        data = self.data.oscillation_data[option_code]
        periods = data['periods']

        if len(periods) < data['required_periods']:
            return None

        # 检查所有周期方向是否一致
        first_direction = periods[0]['direction']
        if all(p['direction'] == first_direction for p in periods):
            signal_type = "买入" if first_direction == 1 else "卖出"

            print(f"?? 震荡信号触发: {option_code} {signal_type}")
            for i, period in enumerate(periods):
                print(f"  周期{i+1}: [{period['start_id']}-{period['end_id']}] "
                      f"{period['start_price']:.4f}→{period['end_price']:.4f} "
                      f"{period['direction_name']}")

            return signal_type

        return None

    def is_oscillation_active(self, option_code):
        """检查震荡检测是否激活"""
        return (option_code in self.data.oscillation_data and
                self.data.oscillation_data[option_code]['active'])

    def end_oscillation_detection(self, option_code, reason):
        """结束震荡检测"""
        if option_code in self.data.oscillation_data:
            data = self.data.oscillation_data[option_code]
            completed_periods = len(data['periods'])
            current_period = data['current_period']
            current_ticks = len(data['current_period_ticks'])
            current_sequence = data['current_period_ticks']

            timestamp = self.get_current_timestamp()
            self.data.oscillation_data[option_code]['active'] = False
            print(f"?? 震荡检测结束: {option_code} [{timestamp}] - {reason}")
            print(f"   最终状态: 完成{completed_periods}个周期, 当前周期{current_period}({current_ticks}tick)")
            print(f"   当前周期序列: {current_sequence}")
            if reason == "周期首尾价格相等" and len(current_sequence) >= 2:
                print(f"   首尾价格: {current_sequence[0]:.4f} == {current_sequence[-1]:.4f}")
            print(f"   震荡检测将等待下次连续计数重置时重新启动")

    def reset_oscillation_detection(self, option_code):
        """重置震荡检测"""
        if option_code in self.data.oscillation_data:
            data = self.data.oscillation_data[option_code]
            completed_periods = len(data['periods'])
            reason = "信号触发成功" if completed_periods >= data['required_periods'] else "连续信号中断"

            del self.data.oscillation_data[option_code]
            print(f"?? 震荡检测重置: {option_code} - {reason}")
            print(f"   完成状态: {completed_periods}个周期完成")
            print(f"   震荡检测将等待下次连续计数重置时重新启动")

    def get_current_timestamp(self, real_timestamp=None):
        """获取当前时间戳 - 回测模式下使用真实历史数据时间"""
        if BACKTEST_CONFIG.get('enable_backtest', False):
            # 回测模式：优先使用传入的真实时间戳
            if real_timestamp:
                return real_timestamp

            # 如果没有真实时间戳，使用存储的当前时间戳
            if hasattr(self, '_current_real_timestamp'):
                return self._current_real_timestamp

            # 最后的备用方案：生成模拟时间
            backtest_date = BACKTEST_CONFIG.get('backtest_start_date', '20250709')
            base_time = datetime.datetime.strptime(backtest_date + '093200', '%Y%m%d%H%M%S')

            if not hasattr(self, '_backtest_tick_counter'):
                self._backtest_tick_counter = 0

            self._backtest_tick_counter += 1
            simulated_time = base_time + datetime.timedelta(seconds=self._backtest_tick_counter * 0.5)

            return simulated_time.strftime('%Y-%m-%d %H:%M:%S')
        else:
            # 实盘模式：使用当前系统时间
            return datetime.datetime.now().strftime('%H:%M:%S.%f')[:-3]

    def set_current_timestamp(self, timestamp):
        """设置当前时间戳（用于回测模式）"""
        self._current_real_timestamp = timestamp

    def print_price_update(self, option_code, timestamp=None):
        """打印价格更新"""
        display_timestamp = self.pm.get('display_timestamp', True)
        signal_threshold = self.pm.get('signal_threshold', 5)
        enable_tick_log = self.pm.get('enable_tick_log', False)

        if timestamp is None:
            timestamp = self.get_current_timestamp()

        if option_code not in self.data.price_chains:
            return

        price_str = "->".join([f"{p:.4f}" for p in self.data.price_chains[option_code]])

        trend_info = ""
        if (option_code in self.data.trend_count and
            self.data.trend_count[option_code] > 0):
            direction_symbol = "↑" if self.data.trend_direction[option_code] == 1 else "↓"
            trend_info = f" [{direction_symbol}{self.data.trend_count[option_code]}/{signal_threshold}]"

        # 根据配置决定是否显示tick日志
        if enable_tick_log:
            if display_timestamp:
                logging.info(f"[{timestamp}] {option_code}: {price_str}{trend_info}")
            else:
                logging.info(f"{option_code}: {price_str}{trend_info}")
        else:
            # 只在控制台显示，不记录到日志
            if display_timestamp:
                print(f"[{timestamp}] {option_code}: {price_str}{trend_info}")
            else:
                print(f"{option_code}: {price_str}{trend_info}")

    def print_signal_alert(self, option_code, signal_type, price, timestamp, sequence):
        """打印信号警报"""
        sequence_str = "->".join([f"{p:.4f}" for p in sequence])
        alert_msg = f"?? [{timestamp}] 触发{signal_type}信号！{option_code}: {sequence_str} (价格: {price:.4f})"

        # 信号警报：重要事件，记录到日志
        logging.warning(f"信号触发: {option_code} {signal_type} {price:.4f}")
        print(alert_msg)

    def validate_tick_timestamp(self, option_code, current_time):
        """验证tick时间戳顺序"""
        try:
            if option_code not in self.data.last_tick_time:
                self.data.last_tick_time[option_code] = current_time
                return True

            last_time = self.data.last_tick_time[option_code]

            # 简单的时间戳验证（假设时间戳是字符串格式）
            if isinstance(current_time, str) and isinstance(last_time, str):
                if current_time < last_time:
                    print(f"?? 时间戳倒退: {option_code} {last_time} -> {current_time}")
                    return False

            self.data.last_tick_time[option_code] = current_time
            return True

        except Exception as e:
            logging.error(f"时间戳验证错误: {e}")
            return True  # 验证失败时允许通过，避免阻塞

    def process_tick_data(self, C, option_code, current_price, current_time, volume=1.0):
        """处理tick数据的完整流程"""
        try:
            # 设置当前真实时间戳（用于回测模式）
            self.set_current_timestamp(current_time)

            # 0. 验证时间戳顺序
            if not self.validate_tick_timestamp(option_code, current_time):
                return

            # 1. 过滤重复tick
            if not self.filter_duplicate_ticks(option_code, current_price, current_time):
                return

            # 2. 分配有效的tick ID（过滤后才分配）
            if option_code not in self.data.current_tick_id:
                self.data.current_tick_id[option_code] = 0
            self.data.current_tick_id[option_code] += 1
            current_tick_id = self.data.current_tick_id[option_code]

            # 2. 更新价格链
            price_chain = self.update_price_chain(option_code, current_price)
            if not price_chain:
                return

            current_price_rounded = price_chain[-1]

            # 2.5. 更新VWAP（每个tick都更新，使用真实成交量加权）
            current_vwap = self.vwap_calculator.update_vwap(option_code, current_price_rounded, volume=volume)

            # 2.6. 更新简化市场检测器
            if hasattr(self.data.batch_manager, 'market_detector') and self.data.batch_manager.market_detector:
                market_state = self.data.batch_manager.market_detector.add_data(
                    current_price_rounded, current_vwap
                )
                # 存储当前市场状态供后续使用
                if not hasattr(self.data, 'current_market_state'):
                    self.data.current_market_state = {}
                self.data.current_market_state[option_code] = market_state
            else:
                # 调试信息：为什么市场检测器没有工作
                if not hasattr(self.data.batch_manager, 'market_detector'):
                    print("❌ 调试：batch_manager没有market_detector属性")
                elif not self.data.batch_manager.market_detector:
                    print("❌ 调试：market_detector为None")
                else:
                    print("❌ 调试：未知的市场检测器问题")

            # 2.7. VWAP定时检查点
            self.check_vwap_at_timepoints(option_code, current_time)
            self.add_manual_vwap_checkpoints(option_code, current_time)

            # 3. 检测连续趋势方向
            direction, count = self.detect_trend_direction(option_code, current_price_rounded)

            # 4. 检查连续趋势信号
            has_continuous_signal, continuous_signal_type = self.check_trend_signal(option_code)

            # 5. 并行检查震荡信号（两种模式独立运行）
            has_oscillation_signal, oscillation_signal_type = self.process_oscillation_tick(
                option_code, current_tick_id, current_price_rounded)

            # 6. 获取时间戳（使用真实历史时间戳）
            timestamp = self.get_current_timestamp()

            # 6.5. 更新VWAP模式分析器的数据（每个tick都积累数据）
            if current_vwap:
                self.vwap_pattern_analyzer.add_data_point(option_code, current_price_rounded, current_vwap, timestamp)

            # 7. 处理信号（允许两种信号同时触发）
            if has_continuous_signal:
                trigger_sequence = self.data.trend_prices[option_code][-5:]

                # 对于买入信号，先检查所有条件再记录和执行
                if continuous_signal_type == "买入":
                    if self.check_all_buy_conditions(C, option_code, f"连续{continuous_signal_type}", current_price_rounded):
                        # 所有条件满足，记录信号并执行
                        self.record_signal(option_code, continuous_signal_type, current_price_rounded, timestamp, trigger_sequence)
                        self.print_signal_alert(option_code, f"连续{continuous_signal_type}", current_price_rounded, timestamp, trigger_sequence)
                        self.execute_buy_order(C, option_code, f"连续{continuous_signal_type}", current_price_rounded)
                    else:
                        print(f"⚠️ 连续{continuous_signal_type}信号被条件检查阻止: {option_code} 价格:{current_price_rounded:.4f}")
                elif continuous_signal_type == "卖出":
                    # 卖出信号无需条件检查，直接记录和执行
                    self.record_signal(option_code, continuous_signal_type, current_price_rounded, timestamp, trigger_sequence)
                    self.print_signal_alert(option_code, f"连续{continuous_signal_type}", current_price_rounded, timestamp, trigger_sequence)
                    self.execute_sell_order(C, option_code, f"连续{continuous_signal_type}", current_price_rounded)

                self.reset_trend_detection(option_code, current_price_rounded)
                # 连续信号触发时，也重置震荡检测
                if self.is_oscillation_active(option_code):
                    self.reset_oscillation_detection(option_code)

            if has_oscillation_signal:
                # 震荡信号触发（可与连续信号同时触发）
                trigger_sequence = [current_price_rounded]

                # 对于买入信号，先检查所有条件再记录和执行
                if oscillation_signal_type == "买入":
                    if self.check_all_buy_conditions(C, option_code, f"震荡{oscillation_signal_type}", current_price_rounded):
                        # 所有条件满足，记录信号并执行
                        self.record_signal(option_code, oscillation_signal_type, current_price_rounded, timestamp, trigger_sequence)
                        self.print_signal_alert(option_code, f"震荡{oscillation_signal_type}", current_price_rounded, timestamp, trigger_sequence)
                        self.execute_buy_order(C, option_code, f"震荡{oscillation_signal_type}", current_price_rounded)
                    else:
                        print(f"⚠️ 震荡{oscillation_signal_type}信号被条件检查阻止: {option_code} 价格:{current_price_rounded:.4f}")
                elif oscillation_signal_type == "卖出":
                    # 卖出信号无需条件检查，直接记录和执行
                    self.record_signal(option_code, oscillation_signal_type, current_price_rounded, timestamp, trigger_sequence)
                    self.print_signal_alert(option_code, f"震荡{oscillation_signal_type}", current_price_rounded, timestamp, trigger_sequence)
                    self.execute_sell_order(C, option_code, f"震荡{oscillation_signal_type}", current_price_rounded)

            # 7.5. 检查平仓信号（在价格更新前）
            self.check_and_execute_exits(option_code, current_price_rounded, current_time)

            # 8. 打印价格更新
            self.print_price_update(option_code, timestamp)

        except Exception as e:
            logging.error(f"处理tick数据错误: {e}")


            # 避免异常分支中引用未定义变量导致新的错误，这里跳过写报告
            pass

    def check_and_execute_exits(self, option_code, current_price, current_time):
        """检查并执行平仓操作"""
        try:
            # 检查是否有持仓需要平仓
            exit_actions = self.data.batch_manager.check_exit_signals(option_code, current_price, current_time)

            if exit_actions:
                print(f"?? 平仓检查 - {option_code} 当前价格:{current_price:.4f}")
                for action in exit_actions:
                    print(f"   批次#{action['batch_id']}: {action['exit_signal']} "
                          f"平仓{action['exit_quantity']}张 剩余{action['remaining_quantity']}张")

                # 可以在这里添加实际的平仓委托逻辑

                # 同步写入报告（中文平仓原因）
                reason_map = {
                    'quick_profit': '快速止盈',
                    'dynamic_tracking': '动态跟踪止盈',
                    'protection': '保护性平仓',
                    'force_exit': '强制平仓',
                    'stop_loss': '止损平仓',
                    'c_bounce_fail': 'C类验证失败/跌破低点',
                        'a_verify_fail': 'A类验证失败',
                        'a_breakdown_below_vwap': 'A类跌回VWAP下方'
                }
                for action in exit_actions:
                    self.report_writer.update_exit(
                        action['batch_id'],
                        {
                            '平仓类型': reason_map.get(action['exit_signal'], action['exit_signal']),
                            '平仓价格': action['exit_price'],
                            '持仓时间(秒)': self._calc_holding_seconds(self.data.batch_manager.get_batch_by_id(action['batch_id']), self.data.batch_manager.get_batch_by_id(action['batch_id'])['exit_strategy']['exit_history'][-1]['exit_time']),
                            '实际盈亏(元)': round(self.data.batch_manager.get_batch_by_id(action['batch_id'])['exit_strategy']['exit_history'][-1]['profit_amount'], 2),
                            '实际盈亏(%)': round(self.data.batch_manager.get_batch_by_id(action['batch_id'])['exit_strategy']['exit_history'][-1]['profit_ratio'], 2),
                            '是否盈利': self.data.batch_manager.get_batch_by_id(action['batch_id'])['exit_strategy']['exit_history'][-1]['profit_amount'] > 0,
                            '平仓状态': '已平仓' if self.data.batch_manager.get_batch_by_id(action['batch_id'])['remaining_quantity'] <= 0 else '部分平仓'
                        }
                    )

                # 目前是回测模式，只记录平仓信息

        except Exception as e:
            print(f"? 平仓检查错误: {option_code} {e}")

    def print_exit_strategy_status(self, option_code, current_price, current_time):
        """打印平仓策略状态（用于监控）"""
        try:
            status_list = self.data.batch_manager.get_exit_strategy_status(option_code, current_price, current_time)

            if status_list:
                print(f"?? 平仓策略状态 - {option_code}")
                for status in status_list:
                    print(f"   批次#{status['batch_id']}: {status['layer']}")
                    print(f"      持仓时间: {status['holding_time']:.0f}秒")
                    print(f"      当前盈利: {status['current_profit']:.2f}元 ({status['current_profit_ratio']:+.2f}%)")
                    print(f"      最大盈利: {status['max_profit']:.2f}元")
                    print(f"      剩余数量: {status['remaining_quantity']}张")

        except Exception as e:
            print(f"? 平仓策略状态查询错误: {option_code} {e}")

    # ==================== 交易功能模块 ====================
    def execute_buy_order(self, C, option_code, signal_type, current_price):
        """执行买入委托 - 回测版本（记录信号，不执行真实交易）"""
        try:
            print(f"?? 回测信号触发: {option_code} {signal_type} 价格:{current_price:.4f}")

            # 检查信号类型，只处理买入信号
            if "卖出" in signal_type:
                print(f"?? 跳过卖出信号: {signal_type} - 不执行买入操作")
                return

            # 获取当前VWAP进行对比
            current_vwap = self.get_current_vwap(C, option_code)
            if current_vwap:
                vwap_diff = current_price - current_vwap
                vwap_diff_pct = (vwap_diff / current_vwap) * 100
                print(f"?? VWAP对比: 当前价格:{current_price:.4f} VWAP:{current_vwap:.4f} "
                      f"差异:{vwap_diff:+.4f} ({vwap_diff_pct:+.2f}%)")

            # 注意：所有条件检查（VWAP过滤器、持仓限制等）已在 check_all_buy_conditions 中完成
            # 这里直接执行买入操作，无需重复检查

            # 记录模拟成交（传递当前真实时间戳）
            current_timestamp = getattr(self, '_current_real_timestamp', None)
            if not current_timestamp:
                print(f"⚠️ 警告: _current_real_timestamp 为空，可能导致时间计算错误")
            batch = self.data.batch_manager.add_batch_from_signal(
                option_code, signal_type, current_price, quantity=1, timestamp=current_timestamp
            )
            # 标注进场模式类别（用于C类验证窗口的平仓规则）
            try:
                sig = self._last_signal_by_code.get(option_code, {})
                pattern = sig.get('vwap_pattern') or {}
                entry_cat = pattern.get('category')
                batch['entry_vwap_category'] = entry_cat
                # 记录入场时的VWAP供A类早切参考
                if current_vwap:
                    batch['entry_vwap'] = float(current_vwap)
                if entry_cat == 'C_bounce':
                    # 记录进场时的局部低点/历史低点，作为防御参考
                    low_price = pattern.get('low_price')
                    if low_price is not None:
                        batch['c_entry_local_low'] = low_price
            except Exception:
                pass

            print(f"? 回测记录: 批次#{batch['batch_id']} {option_code} {signal_type}")
            print(f"   模拟成交价格: {current_price:.4f}")
            print(f"   当前总持仓: {self.data.batch_manager.get_total_position(option_code)}")

            # 显示批次汇总（每5个批次显示一次）
            if len(self.data.batch_manager.batches) % 5 == 0:
                print(f"\n?? 回测进度汇总: 已记录{len(self.data.batch_manager.batches)}个信号")
                self.data.batch_manager.show_all_batches()
            # 写入报告：买入记录
            try:
                # 从最近一次过滤结果获取A/D/E等
                last = self._last_filter_by_code.get(option_code) or self._last_filter_by_code.get('GLOBAL') or {}
                passed = last.get('passed_conditions', [])
                vwap_diff_pct = last.get('vwap_diff_pct', ((current_price - (current_vwap or current_price)) / (current_vwap or current_price) * 100 if current_vwap else 0.0))

                # 使用最近一次信号的模式分析
                sig = self._last_signal_by_code.get(option_code, {})
                pattern = sig.get('vwap_pattern') or {}

                after_pos = self.data.batch_manager.get_total_position(option_code)
                before_pos = max(0, after_pos - batch['quantity'])

                price_diff = batch['entry_price'] - (current_vwap or batch['entry_price'])
                is_c_bounce = (pattern.get('category', '') == 'C_bounce')
                row = {
                    '信号类型': signal_type,
                    '触发时间': current_timestamp or self.get_current_timestamp(),
                    '买入价格': f"{batch['entry_price']:.4f}",
                    '当时VWAP': f"{(current_vwap or 0.0):.4f}",
                    '价格差异': f"{price_diff:+.4f}",
                    '差异百分比': f"{vwap_diff_pct:+.2f}",
                    '实际执行': True,
                    '阻止原因': "",
                    '买入前持仓': f"{before_pos}张",
                    '买入后持仓': f"{after_pos}张",
                    '买入数量': f"{batch['quantity']}张",
                    'VWAP模式类型': pattern.get('type', ''),
                    '买入质量': pattern.get('quality', ''),
                    '时机评估': pattern.get('description', ''),
                    'D反弹(%)': (f"{round(pattern.get('bounce_pct_lowest', 0.0), 2)}" if is_c_bounce else ''),
                    'E反弹(%)': (f"{round(pattern.get('bounce_pct_local', 0.0), 2)}" if is_c_bounce else ''),
                    '模式分类': pattern.get('category', ''),
                    '触发序列': (f"震荡信号触发价格: {batch['entry_price']:.4f}" if ('震荡' in signal_type) else (sig.get('sequence') if isinstance(sig.get('sequence'), str) else ("→".join([f"{p:.4f}" for p in sig.get('sequence', [])]) if sig.get('sequence') else ''))),
                    '震荡周期信息': ('震荡信号' if ('震荡' in signal_type) else '非震荡信号'),
                    '通过A类条件': ('A类' in passed),
                    '通过D类条件': ('D类' in passed),
                    '通过E类条件': ('E类' in passed),
                    '被B类排除': False,
                    '最终通过': bool(passed),
                    '过滤原因': last.get('reason', ''),
                    '平仓类型': '', '平仓价格': 0, '持仓时间(秒)': 0,
                    '实际盈亏(元)': 0, '实际盈亏(%)': 0, '是否盈利': '', '平仓状态': '未平仓或数据缺失'
                }
                self.report_writer.add_signal(row, batch['batch_id'])
            except Exception as e:
                print(f"? 写报告失败(买入): {option_code} {e}")


                print()


        except Exception as e:
            print(f"? 回测记录异常: {option_code} {e}")
            logging.error(f"回测记录异常: {option_code} {e}")

    def execute_sell_order(self, C, option_code, signal_type, current_price):
        """执行卖出委托 - 回测版本（仅记录信号，不实际交易）"""
        try:
            print(f"?? 回测卖出信号触发: {option_code} {signal_type} 价格:{current_price:.4f}")

            # 获取当前VWAP进行对比
            current_vwap = self.get_current_vwap(C, option_code)
            if current_vwap:
                vwap_diff = current_price - current_vwap
                vwap_diff_pct = (vwap_diff / current_vwap) * 100
                print(f"?? VWAP对比: 当前价格:{current_price:.4f} VWAP:{current_vwap:.4f} "
                      f"差异:{vwap_diff:+.4f} ({vwap_diff_pct:+.2f}%)")

            # 卖出信号不需要检查持仓限制，也不需要VWAP过滤器
            # 在回测模式下，卖出信号仅用于记录和分析，不实际执行交易
            print(f"?? 卖出信号已记录: {option_code} {signal_type} - 仅用于分析，不执行实际交易")

        except Exception as e:
            print(f"? 卖出信号记录异常: {option_code} {e}")
            logging.error(f"卖出信号记录异常: {option_code} {e}")

    def get_current_vwap(self, C, option_code):
        """获取当前VWAP价格"""
        try:
            vwap = self.vwap_calculator.calculate_vwap(C, option_code)
            return vwap
        except Exception as e:
            print(f"? 获取VWAP失败: {e}")
            return None

    def check_vwap_at_timepoints(self, option_code, current_time):
        """在特定时间点检查并输出VWAP值"""
        try:
            # 解析当前时间
            if isinstance(current_time, str):
                time_obj = datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S')
            else:
                return

            # 定义检查点：每半小时和整点
            check_minutes = [0, 30]  # 整点和半点
            current_minute = time_obj.minute
            current_second = time_obj.second

            # 检查是否是目标时间点（允许几秒误差）
            if current_minute in check_minutes and current_second <= 5:
                checkpoint_key = f"{time_obj.hour:02d}:{current_minute:02d}"

                # 避免重复输出同一时间点
                if checkpoint_key not in self.vwap_checkpoints:
                    self.vwap_checkpoints.add(checkpoint_key)

                    # 获取VWAP详细信息
                    vwap_details = self.vwap_calculator.get_vwap_details(option_code)
                    if vwap_details:
                        # 获取当前价格（从价格链中获取最新价格）
                        current_price = None
                        if option_code in self.data.price_chains and len(self.data.price_chains[option_code]) > 0:
                            current_price = self.data.price_chains[option_code][-1]

                        print(f"\n{'='*80}")
                        print(f"?? VWAP检查点 - {time_obj.strftime('%Y-%m-%d %H:%M:%S')}")
                        print(f"{'='*80}")
                        print(f"合约代码: {option_code}")
                        print(f"当前价格: {current_price:.4f}" if current_price else "当前价格: 获取失败")

                        # 显示双VWAP对比
                        strategy_vwap = vwap_details['vwap']
                        reference_vwap = self.vwap_calculator.get_reference_vwap(option_code)

                        print(f"策略VWAP (过滤后): {strategy_vwap:.6f}")
                        if reference_vwap:
                            print(f"参考VWAP (所有tick): {reference_vwap:.6f}")
                            vwap_diff = strategy_vwap - reference_vwap
                            vwap_diff_pct = (vwap_diff / reference_vwap) * 100 if reference_vwap > 0 else 0
                            print(f"VWAP差异: {vwap_diff:+.6f} ({vwap_diff_pct:+.2f}%)")

                        print(f"累计成交金额: {vwap_details['total_value']:.2f}")
                        print(f"累计成交量增量: {vwap_details['total_volume']:.0f}")
                        print(f"有效成交tick数: {vwap_details['tick_count']}")
                        if vwap_details['tick_count'] > 0:
                            print(f"平均每tick成交量增量: {vwap_details['total_volume']/vwap_details['tick_count']:.1f}")
                        else:
                            print(f"平均每tick成交量增量: 0.0 (无有效成交)")

                        if current_price:
                            diff = current_price - strategy_vwap
                            diff_pct = (diff / strategy_vwap) * 100 if strategy_vwap > 0 else 0
                            print(f"价格偏离策略VWAP: {diff:+.4f} ({diff_pct:+.2f}%)")

                        print(f"?? 请对比K线图中 {checkpoint_key} 的VWAP值")
                        print(f"?? 参考VWAP更接近K线图，策略VWAP用于交易逻辑")

                        print(f"{'='*80}\n")

        except Exception as e:
            print(f"? VWAP检查点错误: {e}")

    def add_manual_vwap_checkpoints(self, option_code, current_time):
        """添加手动VWAP检查点（用于特定时间验证）"""
        try:
            if isinstance(current_time, str):
                time_obj = datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S')
            else:
                return

            # 特定验证时间点
            verification_times = [
                "14:01:00", "14:01:04",  # 你提到的时间点
                "14:12:00", "14:12:02",
                "14:43:00", "14:43:04",
                "10:00:00", "10:30:00",  # 额外的整点半点
                "11:00:00", "11:30:00",
                "13:00:00", "13:30:00",
                "14:00:00", "14:30:00"
            ]

            current_time_str = time_obj.strftime('%H:%M:%S')

            # 检查是否接近验证时间点（允许5秒误差）
            for verify_time in verification_times:
                verify_obj = datetime.strptime(verify_time, '%H:%M:%S')
                current_check = datetime.strptime(current_time_str, '%H:%M:%S')

                time_diff = abs((current_check - verify_obj).total_seconds())

                if time_diff <= 5:  # 5秒内
                    checkpoint_key = f"manual_{verify_time}"

                    if checkpoint_key not in self.vwap_checkpoints:
                        self.vwap_checkpoints.add(checkpoint_key)

                        vwap_details = self.vwap_calculator.get_vwap_details(option_code)
                        if vwap_details:
                            # 获取当前价格（从价格链中获取最新价格）
                            current_price = None
                            if option_code in self.data.price_chains and len(self.data.price_chains[option_code]) > 0:
                                current_price = self.data.price_chains[option_code][-1]

                            print(f"\n?? 手动VWAP验证点 - {current_time}")
                            print(f"目标时间: {verify_time} (实际: {current_time_str}, 误差: {time_diff:.1f}秒)")
                            print(f"当前VWAP: {vwap_details['vwap']:.6f}")
                            print(f"当前价格: {current_price:.4f}" if current_price else "当前价格: 获取失败")
                            print(f"累计tick数: {vwap_details['tick_count']}")
                            print(f"请对比K线图中 {verify_time} 的VWAP值")
                            print("-" * 60)

        except Exception as e:
            print(f"? 手动VWAP检查点错误: {e}")



    def check_all_buy_conditions(self, C, option_code, signal_type, current_price):
        """检查所有买入条件 - 整合持仓限制、VWAP过滤器等所有条件"""
        try:
            print(f"🔍 检查买入条件: {option_code} {signal_type} 价格:{current_price:.4f}")

            # 1. 检查持仓限制（含待成交委托）
            max_position = self.pm.get('max_position_per_contract')
            current_position = self.data.batch_manager.get_total_position(option_code)
            # 统计该合约的待成交买入数量
            pending_qty = 0
            try:
                pending_qty = sum(
                    info.get('target_quantity', 0)
                    for info in self.data.batch_manager.pending_orders.values()
                    if info.get('option_code') == option_code and info.get('status') == 'pending'
                )
            except Exception:
                pending_qty = 0

            total_after_pending = current_position + pending_qty
            if total_after_pending >= max_position:
                print(f"❌ 持仓已满(含待成交): {option_code} 当前:{current_position} 待成交:{pending_qty} 最大:{max_position}")
                return False

            # 2. 检查VWAP过滤器
            current_vwap = self.get_current_vwap(C, option_code)
            if current_vwap and self.vwap_filter.enabled:
                vwap_diff = current_price - current_vwap
                vwap_diff_pct = (vwap_diff / current_vwap) * 100
                print(f"🔍 VWAP对比: 当前价格:{current_price:.4f} VWAP:{current_vwap:.4f} "
                      f"差异:{vwap_diff:+.4f} ({vwap_diff_pct:+.2f}%)")

                # 获取VWAP模式分析结果
                vwap_pattern = self.vwap_pattern_analyzer.analyze_pattern(
                    option_code, current_price, current_vwap
                )

                # 应用VWAP过滤器（价格自适应版本）
                should_buy, filter_reason = self.vwap_filter.should_buy(
                    buy_price=current_price,
                    current_vwap=current_vwap,
                    vwap_pattern=vwap_pattern,
                    option_code=option_code
                )

                if not should_buy:
                    print(f"❌ VWAP过滤器拒绝: {filter_reason}")
                    return False
                else:
                    print(f"✅ VWAP过滤器通过: {filter_reason}")
                    # 解析通过项并缓存，供报告写入
                    try:
                        tokens = [t.strip() for t in filter_reason.replace('通过:', '').split(',') if t.strip()]
                        self._last_filter_by_code[option_code] = {
                            'passed_conditions': tokens,
                            'final_pass': True,
                            'reason': filter_reason,
                            'vwap_diff_pct': round(vwap_diff_pct, 2)
                        }
                    except Exception:
                        self._last_filter_by_code[option_code] = {
                            'passed_conditions': [],
                            'final_pass': True,
                            'reason': filter_reason,
                            'vwap_diff_pct': round(vwap_diff_pct, 2)
                        }

            # 3. 其他条件检查可以在这里添加
            # 例如：时间限制、风险控制等

            print(f"✅ 所有买入条件检查通过: {option_code}")
            return True

        except Exception as e:
            print(f"❌ 买入条件检查异常: {option_code} {e}")
            return False

    def check_buy_conditions_with_batch(self, C, option_code, max_position):
        """检查买入条件 - 使用批次管理器 + 实际持仓验证（保留原方法用于兼容性）"""
        try:
            print(f"?? 检查买入条件: {option_code}")

            # 1. 获取本地记录的持仓
            local_position = self.data.batch_manager.get_total_position(option_code)

            # 2. 获取QMT实际持仓进行验证
            real_position = self.get_current_position(C, option_code)

            # 3. 持仓同步逻辑
            if local_position != real_position:
                print(f"?? 检测到持仓不一致: 本地记录{local_position} vs QMT实际{real_position}")
                self.sync_position_records(C, option_code, local_position, real_position)
                # 更新后重新获取本地持仓
                local_position = self.data.batch_manager.get_total_position(option_code)
                print(f"?? 同步完成，当前本地持仓: {local_position}")

            # 4. 使用同步后的持仓进行判断
            current_position = local_position
            pending_quantity = len(self.data.batch_manager.pending_orders)

            print(f"   当前持仓:{current_position} 待成交:{pending_quantity} 最大:{max_position}")

            if current_position + pending_quantity >= max_position:
                print(f"? 持仓已满: {option_code} 当前:{current_position} 待成交:{pending_quantity} 最大:{max_position}")
                return False

            print(f"? 买入条件检查通过: {option_code}")
            return True

        except Exception as e:
            print(f"? 买入条件检查异常: {option_code} {e}")
            return False

    def sync_position_records(self, C, option_code, local_position, real_position):
        """同步本地批次记录与实际持仓"""
        try:
            if real_position == 0 and local_position > 0:
                # 情况1: 实际已清仓，但本地还有记录 → 清除本地记录
                print(f"?? 实际已清仓，清除本地批次记录: {option_code}")
                self.data.batch_manager.clear_option_batches(option_code)

            elif real_position > 0 and local_position == 0:
                # 情况2: 实际有持仓，但本地无记录 → 创建估算批次
                print(f"?? 实际有持仓但本地无记录，创建估算批次: {option_code} 数量:{real_position}")
                estimated_price = self.get_current_market_price(C, option_code)
                self.data.batch_manager.create_estimated_batch(option_code, real_position, estimated_price)

            elif real_position > 0 and local_position > real_position:
                # 情况3: 部分平仓 → 调整本地记录
                closed_quantity = local_position - real_position
                print(f"?? 检测到部分平仓，调整本地记录: {option_code} 平仓数量:{closed_quantity}")
                self.data.batch_manager.adjust_batches_for_partial_close(option_code, closed_quantity)

            elif real_position > local_position:
                # 情况4: 实际持仓更多 → 补充记录
                additional_quantity = real_position - local_position
                print(f"?? 实际持仓更多，补充批次记录: {option_code} 补充数量:{additional_quantity}")
                estimated_price = self.get_current_market_price(C, option_code)
                self.data.batch_manager.create_estimated_batch(option_code, additional_quantity, estimated_price)

        except Exception as e:
            print(f"? 持仓同步异常: {option_code} {e}")

    def get_current_market_price(self, C, option_code):
        """获取当前市价（用于估算批次）"""
        try:
            # 尝试获取最新价格
            tick_data = C.get_full_tick([option_code])
            if option_code in tick_data:
                price = tick_data[option_code]['lastPrice']
                if hasattr(price, 'item'):
                    return float(price.item())
                return float(price)

            # 如果获取失败，返回默认估算价格
            print(f"?? 无法获取 {option_code} 的当前价格，使用默认估算价格")
            return 0.05  # 默认估算价格

        except Exception as e:
            print(f"? 获取市价失败: {option_code} {e}")
            return 0.05  # 默认估算价格

    def calculate_buy_quantity_with_batch(self, C, option_code, market_data, max_position):
        """计算买入数量 - 考虑部分成交和未完成数量"""
        try:
            current_position = self.data.batch_manager.get_total_position(option_code)
            pending_quantity = len(self.data.batch_manager.pending_orders)

            # 检查是否有未完成的买入数量
            remaining_quantity = self.data.pending_buy_quantities.get(option_code, 0)
            if remaining_quantity > 0:
                print(f"?? 发现未完成买入: {option_code} 剩余数量:{remaining_quantity}")
                target_quantity = min(remaining_quantity, market_data['ask_volume'])
            else:
                available_quantity = max_position - current_position - pending_quantity
                target_quantity = min(available_quantity, market_data['ask_volume'])

            print(f"?? 买入数量计算: {option_code} 卖1量:{market_data['ask_volume']} "
                  f"当前持仓:{current_position} 最大:{max_position} 目标:{target_quantity}")

            return max(0, target_quantity)

        except Exception as e:
            print(f"? 计算买入数量异常: {option_code} {e}")
            return 0

    def place_buy_order_with_batch_management(self, C, option_code, quantity, market_data, signal_type, max_position):
        """下买入委托并管理批次"""
        try:
            # 执行买入委托 - passorder是全局函数，不是C对象的方法
            order_result = passorder(
                50,                              # op_type: 50=期权买入开仓
                1101,                            # order_mode: 1101=按股数
                account,                         # account_id: 全局账户变量
                option_code,                     # contract: 合约代码
                5,                               # price_type: 5=市价
                market_data['ask_price'],        # exec_price: 执行价格（市价单也需要填写）
                quantity,                        # volume: 数量
                "期权策略",                       # strategy_name: 策略名称
                2,                               # quicktrade: 2=立即下单
                f"期权买入-{option_code}",        # msg: 备注信息
                C                                # C: 上下文对象作为最后一个参数
            )

            print(f"? 委托下单请求已发送: {option_code} 数量:{quantity} 价格:{market_data['ask_price']:.4f}")

            print(f"?? 委托返回结果: {order_result} (类型: {type(order_result)})")

            # 根据QMT官方文档：passorder返回值是'无'，不能依赖返回值判断成功失败
            # QMT使用异步交易机制，委托状态通过回调函数获取
            print(f"?? 委托请求已发送: {option_code} 数量:{quantity} 价格:{market_data['ask_price']:.4f}")
            print("? 等待回调函数确认委托状态...")

            # 记录委托尝试（不依赖返回值）
            try:
                # 生成临时委托ID，等待回调函数更新为真实委托号
                temp_order_id = f"{option_code}_{int(time.time())}"

                self.data.batch_manager.add_pending_order(
                    temp_order_id, option_code, market_data['ask_price'], quantity
                )
                print(f"?? 委托记录成功: {temp_order_id}")
                print("?? 等待order_callback和deal_callback确认...")

                # 记录委托时间，用于后续验证
                if not hasattr(self.data, 'pending_verifications'):
                    self.data.pending_verifications = []

                self.data.pending_verifications.append({
                    'temp_order_id': temp_order_id,
                    'option_code': option_code,
                    'target_quantity': quantity,
                    'target_price': market_data['ask_price'],
                    'timestamp': time.time()
                })

            except Exception as e:
                print(f"?? 记录委托失败: {e}")

            # 处理未完成数量
            current_position = self.data.batch_manager.get_total_position(option_code)
            original_target = max_position - current_position
            if quantity < original_target:
                # 记录未完成的数量，等待下次信号
                self.data.pending_buy_quantities[option_code] = original_target - quantity
                print(f"?? 记录未完成买入: {option_code} 剩余:{self.data.pending_buy_quantities[option_code]}")
            else:
                # 清除未完成数量
                self.data.pending_buy_quantities.pop(option_code, None)

        except Exception as e:
            print(f"? 下单异常: {option_code} {e}")
            logging.error(f"下单异常: {option_code} {e}")



    def get_market_data(self, C, option_code):
        """获取市场数据"""
        try:
            # 方法1: 尝试使用get_market_data_ex获取五档行情
            try:
                market_data = C.get_market_data_ex([option_code], period='tick', count=1, subscribe=True)
                if market_data and option_code in market_data and len(market_data[option_code]) > 0:
                    tick_data = market_data[option_code][-1]

                    # 根据官方文档，五档行情字段为askPrice/askVol (list类型)
                    ask_prices = tick_data.get('askPrice', [])
                    ask_volumes = tick_data.get('askVol', [])
                    bid_prices = tick_data.get('bidPrice', [])
                    bid_volumes = tick_data.get('bidVol', [])

                    result = {
                        'ask_price': ask_prices[0] if ask_prices else 0,      # 卖1价
                        'ask_volume': ask_volumes[0] if ask_volumes else 0,    # 卖1量
                        'bid_price': bid_prices[0] if bid_prices else 0,      # 买1价
                        'bid_volume': bid_volumes[0] if bid_volumes else 0,    # 买1量
                        'last_price': tick_data.get('lastPrice', 0)           # 最新价
                    }

                    print(f"?? 市场数据(订阅): {option_code} 卖1:{result['ask_price']:.4f}({result['ask_volume']}) 买1:{result['bid_price']:.4f}({result['bid_volume']})")
                    return result
            except Exception as e1:
                print(f"?? 订阅方式获取行情失败: {e1}")

            # 方法2: 使用get_full_tick获取基础行情
            full_tick = C.get_full_tick([option_code])
            if full_tick and option_code in full_tick:
                tick_data = full_tick[option_code]

                # 根据官方文档，五档行情字段为askPrice/askVol (list类型)
                ask_prices = tick_data.get('askPrice', [])
                ask_volumes = tick_data.get('askVol', [])
                bid_prices = tick_data.get('bidPrice', [])
                bid_volumes = tick_data.get('bidVol', [])

                result = {
                    'ask_price': ask_prices[0] if ask_prices else tick_data.get('lastPrice', 0),      # 卖1价，如果没有则用最新价
                    'ask_volume': ask_volumes[0] if ask_volumes else 100,    # 卖1量，如果没有则假设100
                    'bid_price': bid_prices[0] if bid_prices else tick_data.get('lastPrice', 0),      # 买1价，如果没有则用最新价
                    'bid_volume': bid_volumes[0] if bid_volumes else 100,    # 买1量，如果没有则假设100
                    'last_price': tick_data.get('lastPrice', 0)      # 最新价
                }

                print(f"?? 市场数据(全推): {option_code} 卖1:{result['ask_price']:.4f}({result['ask_volume']}) 买1:{result['bid_price']:.4f}({result['bid_volume']})")
                return result

            print(f"? 无法获取市场数据: {option_code}")
            return None

        except Exception as e:
            print(f"? 获取市场数据异常: {option_code} {e}")
            return None





    def get_current_position(self, C, option_code):
        """获取当前持仓"""
        try:
            # 使用QMT API获取持仓 - get_trade_detail_data是全局函数
            positions = get_trade_detail_data(account, 'STOCK_OPTION', 'POSITION', C)
            if not positions:
                return 0

            for position in positions:
                if hasattr(position, 'm_strInstrumentID') and position.m_strInstrumentID == option_code.split('.')[0]:
                    return getattr(position, 'm_nVolume', 0)

            return 0

        except Exception as e:
            print(f"? 获取持仓异常: {option_code} {e}")
            return 0



    def get_available_cash(self, C):
        """获取可用资金"""
        try:
            # 使用QMT API获取账户资金 - get_trade_detail_data是全局函数
            accounts = get_trade_detail_data(account, 'STOCK_OPTION', 'ACCOUNT', C)
            if accounts and len(accounts) > 0:
                return getattr(accounts[0], 'm_dAvailable', 0)
            return 0

        except Exception as e:
            print(f"? 获取可用资金异常: {e}")
            return 0





    def check_pending_verifications(self, C):
        """检查待验证的委托（备用方案）"""
        if not hasattr(self.data, 'pending_verifications'):
            return

        # ?? 首先检查委托超时并撤单
        self.check_and_cancel_timeout_orders(C)

        current_time = time.time()

        for verification in self.data.pending_verifications[:]:  # 复制列表避免修改问题
            # 如果超过30秒还没有回调，主动查询
            if current_time - verification['timestamp'] > 30:
                print(f"? 委托超时，启动主动验证: {verification['temp_order_id']}")

                try:
                    # 查询持仓变化
                    option_code = verification['option_code']
                    current_position = self.get_current_position(option_code, C)

                    # 如果持仓增加，推断交易成功
                    if current_position > 0:
                        print(f"?? 检测到持仓增加: {option_code} 持仓:{current_position}")
                        print("?? 推断交易成功，手动创建批次记录")

                        # 手动创建批次记录
                        self.create_manual_batch(verification)

                    else:
                        print(f"?? 未检测到持仓变化: {option_code}")
                        print("?? 可能交易失败或还在处理中")

                except Exception as e:
                    print(f"? 主动验证失败: {e}")

                # 移除已检查的项目
                self.data.pending_verifications.remove(verification)

    def create_manual_batch(self, verification):
        """手动创建批次记录（当回调函数不工作时）"""
        try:
            # 创建手动批次（与回测批次结构保持一致，支持平仓与持仓统计）
            batch = {
                'batch_id': len(self.data.batch_manager.batches) + 1,
                'option_code': verification['option_code'],
                'order_id': verification['temp_order_id'],
                'signal_type': '手动创建',
                'entry_price': verification['target_price'],  # 使用目标价格
                'price': verification['target_price'],        # 兼容平仓策略使用的字段
                'quantity': int(verification['target_quantity']),   # 使用目标数量
                'remaining_quantity': int(verification['target_quantity']),
                'cost': verification['target_price'] * verification['target_quantity'] * 10000,
                'commission': 5.0,  # 估算手续费
                'trade_time': time.strftime('%H:%M:%S'),
                'trade_date': time.strftime('%Y-%m-%d'),
                'time': time.strftime('%Y-%m-%d %H:%M:%S'),   # 兼容平仓策略的时间字段
                'timestamp': time.time(),
                'source': 'manual_verification',  # 标记为主动验证创建
                'exit_strategy': {
                    'max_profit': None,
                    'max_profit_ratio': None,
                    'fully_closed': False,
                    'exit_history': []
                }
            }

            self.data.batch_manager.batches.append(batch)

            print(f"?? 手动批次创建成功: {verification['option_code']}")
            print(f"   批次#{batch['batch_id']} 价格:{batch['entry_price']:.4f} 数量:{batch['quantity']}")
            print(f"   来源: 主动验证（回调函数未触发）")

            # 显示当前持仓汇总
            total_position = self.data.batch_manager.get_total_position(verification['option_code'])
            avg_cost = self.data.batch_manager.get_average_cost(verification['option_code'])
            print(f"?? 持仓汇总: {verification['option_code']}")
            print(f"   总持仓: {total_position}")
            print(f"   平均成本: {avg_cost:.4f}")

        except Exception as e:
            print(f"? 手动批次创建失败: {e}")

    def check_and_cancel_timeout_orders(self, C):
        """检查并撤销超时委托"""
        try:
            if not hasattr(self.data, 'batch_manager') or not self.data.batch_manager.pending_orders:
                return

            current_time = time.time()
            timeout_seconds = self.pm.get('order_timeout_seconds')  # 统一从参数管理器获取
            timeout_orders = []

            # 检查所有待成交委托
            for order_id, order_info in self.data.batch_manager.pending_orders.items():
                if order_info['status'] == 'pending':
                    elapsed_time = current_time - order_info['timestamp']
                    if elapsed_time > timeout_seconds:
                        # ?? 额外安全检查：如果有真实委托号，先检查是否已经有对应的批次记录
                        real_order_id = order_info.get('real_order_id')
                        if real_order_id:
                            # 检查是否已经有该委托号的批次记录（说明已成交）
                            already_executed = any(
                                batch.get('order_id') == real_order_id
                                for batch in self.data.batch_manager.batches
                            )
                            if already_executed:
                                print(f"?? 跳过已成交委托的撤单: {order_id} 真实委托号:{real_order_id}")
                                # 直接清理该待成交记录，不执行撤单
                                self.data.batch_manager.pending_orders[order_id]['status'] = 'completed'
                                continue

                        timeout_orders.append((order_id, order_info, elapsed_time))

            # 撤销超时委托
            for order_id, order_info, elapsed_time in timeout_orders:
                option_code = order_info['option_code']
                print(f"? 检测到超时委托: {option_code} 委托:{order_id} 已等待:{elapsed_time:.1f}秒")

                # 获取真实委托号（如果有的话）
                real_order_id = order_info.get('real_order_id', order_id)

                # 尝试撤销委托
                if self.cancel_order(C, real_order_id):
                    print(f"? 撤销超时委托成功: {option_code} 真实委托号:{real_order_id}")
                    # 更新委托状态
                    self.data.batch_manager.pending_orders[order_id]['status'] = 'cancelled'
                    # 从待成交列表中移除
                    del self.data.batch_manager.pending_orders[order_id]
                else:
                    print(f"? 撤销委托失败: {option_code} 真实委托号:{real_order_id}")

            # ?? 清理已完成状态的委托记录
            completed_orders = [
                order_id for order_id, order_info in self.data.batch_manager.pending_orders.items()
                if order_info['status'] == 'completed'
            ]
            for order_id in completed_orders:
                del self.data.batch_manager.pending_orders[order_id]
                print(f"?? 清理已完成委托记录: {order_id}")

        except Exception as e:
            print(f"? 检查超时委托异常: {e}")

    def cancel_order(self, C, order_id):
        """撤销委托"""
        try:
            # 使用QMT API撤销委托 - cancel是全局函数
            cancel_result = cancel(order_id, account, 'STOCK_OPTION', C)

            if cancel_result:
                print(f"?? 撤单API调用成功: {order_id}")
                return True
            else:
                print(f"?? 撤单API调用失败: {order_id}")
                return False

        except Exception as e:
            print(f"? 撤销委托异常: {order_id} {e}")
            return False

# ==================== 全局监控对象 ====================
monitor = OptionMonitor()

# ==================== QMT全局变量 ====================
# ==================== QMT回测主程序 ====================
def init(ContextInfo):
    """初始化函数 - 回测版本（参考精确VWAP验证.py）"""
    print("?? QMT期权策略回测版本启动")
    print("=" * 60)

    # 关键：设置测试参数到ContextInfo（与精确VWAP验证.py一致）
    ContextInfo.test_option = monitor.pm.get('test_option_code', '********.SHO')
    ContextInfo.test_date = BACKTEST_CONFIG['backtest_start_date']

    print(f"?? 测试合约: {ContextInfo.test_option}")
    print(f"?? 测试日期: {ContextInfo.test_date}")

    # 回测模式配置
    if BACKTEST_CONFIG['enable_backtest']:
        print("?? 回测模式配置:")
        print(f"  - 回测开始日期: {BACKTEST_CONFIG['backtest_start_date']}")
        print(f"  - 回测结束日期: {BACKTEST_CONFIG['backtest_end_date']}")
        print(f"  - 自动下载历史数据: {BACKTEST_CONFIG['download_history_data']}")

        # QMT回测模式会自动处理历史数据
        print("?? QMT回测模式：历史数据将自动加载")
        print("?? 请确保在QMT界面设置了正确的回测日期范围")

    # 显示策略参数
    print("\n?? 策略参数:")
    print(f"  - 标的代码: {monitor.data.underlying_code}")
    print(f"  - 信号阈值: 连续{monitor.pm.get('signal_threshold', 5)}个同方向tick触发信号")
    print(f"  - 震荡检测: {monitor.pm.get('oscillation_periods', 3)}个周期，每周期{monitor.pm.get('oscillation_period_size', 5)}个tick")
    print(f"  - 测试期权: {monitor.pm.get('test_option_code', '********.SHO')}")

    print("\n?? 回测设置:")
    print(f"  - 真实交易: 关闭 (回测模式)")
    print(f"  - 数据存储: 内存模式 (无持久化)")
    print(f"  - 日志级别: 简化模式")

    print("=" * 60)
    print("? 初始化完成，等待历史数据加载...")

    # 记录启动信息
    logging.info(f"回测启动 - 标的:{monitor.data.underlying_code} 日期:{BACKTEST_CONFIG['backtest_start_date']}-{BACKTEST_CONFIG['backtest_end_date']}")

def after_init(ContextInfo):
    """初始化后执行 - 回测版本"""
    try:
        print("?? 回测模式：开始设置监控合约...")

        # 回测模式使用固定的测试期权
        test_option = monitor.pm.get('test_option_code', '********.SHO')
        monitor.data.selected_options = [test_option]

        print(f"? 回测合约设置: {test_option}")
        print("?? 回测模式将通过handlebar函数处理历史数据")

        # 测试数据获取
        try:
            print("?? 测试数据获取...")
            tick_data = ContextInfo.get_full_tick([test_option])
            if test_option in tick_data:
                tick_info = tick_data[test_option]
                current_price = tick_info.get('lastPrice', 0)
                print(f"? 成功获取当前价格: {current_price}")
            else:
                print(f"?? 未获取到 {test_option} 的tick数据")
        except Exception as e:
            print(f"?? 数据获取测试失败: {e}")

        # 显示监控规则
        signal_threshold = monitor.pm.get('signal_threshold', 5)
        oscillation_periods = monitor.pm.get('oscillation_periods', 3)
        oscillation_period_size = monitor.pm.get('oscillation_period_size', 5)

        print(f"\n?? 监控规则:")
        print(f"  - 连续信号: 连续{signal_threshold}个同方向tick触发")
        print(f"  - 震荡信号: {oscillation_periods}个周期，每周期{oscillation_period_size}个tick")
        print(f"  - 价格链长度: {monitor.pm.get('max_chain_length', 30)}个tick")

        print("\n?? 回测准备完成，等待历史数据...")
        print("=" * 60)

    except Exception as e:
        print(f"? 回测初始化失败: {e}")
        logging.error(f"回测初始化失败: {e}")

def handlebar(ContextInfo):
    """K线回调函数 - 回测版本（使用历史分笔数据）"""
    try:
        # 回测模式下，处理每根K线（移除is_last_bar限制）
        # 这样可以获取到历史数据进行动态计算

        # 只在第一次调用时执行完整的回测流程
        if not hasattr(ContextInfo, '_backtest_executed'):
            ContextInfo._backtest_executed = True
        else:
            return  # 避免重复执行

        print("\n" + "="*60)
        print("?? QMT期权策略回测 - 处理历史数据")
        print("="*60)

        # 获取测试合约和日期（使用ContextInfo中设置的参数，与精确VWAP验证.py一致）
        test_option = getattr(ContextInfo, 'test_option', '********.SHO')
        today_str = getattr(ContextInfo, 'test_date', '20250709')

        # 关键修复：使用当前K线的时间作为end_time（参考官方文档回测示例）
        try:
            current_bar_time = ContextInfo.get_bar_timetag(ContextInfo.barpos)
            if hasattr(current_bar_time, 'strftime'):
                current_time_str = current_bar_time.strftime('%Y%m%d%H%M%S')
            else:
                # 如果获取不到当前时间，使用默认时间
                current_time_str = today_str + '150000'
        except:
            current_time_str = today_str + '150000'

        print(f"?? 回测日期: {today_str}")
        print(f"?? 测试合约: {test_option}")
        print(f"? 当前时间: {current_time_str}")

        # 使用历史分笔数据获取方法（完全参考精确VWAP验证.py）
        try:
            print(f"?? 获取 {test_option} 的历史分笔数据...")

            # 从开盘时间09:30开始获取数据（VWAP应该从开盘开始计算）
            tick_data = ContextInfo.get_market_data_ex(
                fields=['lastPrice', 'volume', 'amount'],
                stock_code=[test_option],
                period='tick',
                start_time=today_str + '093000',  # 从9:30开盘开始，VWAP需要从开盘计算
                end_time=current_time_str,        # 到当前时间
                count=-1,
                subscribe=False  # 历史数据不需要订阅
            )

            print(f"?? 数据获取结果: {list(tick_data.keys()) if tick_data else '无数据'}")

            if tick_data and test_option in tick_data:
                df = tick_data[test_option]
                print(f"?? 数据框信息: 形状={df.shape if hasattr(df, 'shape') else 'N/A'}")

                if hasattr(df, 'empty') and not df.empty and len(df) > 0:
                    print(f"?? 获取到{len(df)}条分笔数据")

                    # 初始化VWAP计算器（只重置一次）
                    monitor.vwap_calculator.reset_vwap(test_option)
                    print(f"?? VWAP计算器已重置: {test_option}")

                    # 处理每个tick数据
                    processed_count = 0

                    for index, row in df.iterrows():
                        try:
                            price = float(row['lastPrice'])
                            volume = float(row.get('volume', 1.0))  # 获取真实成交量
                            if price > 0:
                                # 获取真实的历史时间戳
                                real_timestamp = None

                                # 从QMT索引中解析真实时间戳
                                # QMT索引格式: 20250704093200.230 (YYYYMMDDHHMMSS.mmm)
                                index_str = str(index)
                                if len(index_str) >= 14:
                                    try:
                                        # 解析日期和时间部分
                                        date_part = index_str[:8]    # 20250704
                                        time_part = index_str[8:14]  # 093200

                                        # 格式化为标准时间戳
                                        year = date_part[:4]
                                        month = date_part[4:6]
                                        day = date_part[6:8]
                                        hour = time_part[:2]
                                        minute = time_part[2:4]
                                        second = time_part[4:6]

                                        real_timestamp = f"{year}-{month}-{day} {hour}:{minute}:{second}"
                                    except Exception as e:
                                        # 备用方案：使用索引位置生成近似时间
                                        base_time = datetime.strptime(today_str + '093200', '%Y%m%d%H%M%S')
                                        from datetime import timedelta
                                        approx_time = base_time + timedelta(seconds=processed_count * 0.5)
                                        real_timestamp = approx_time.strftime('%Y-%m-%d %H:%M:%S')
                                else:
                                    # 备用方案：使用索引位置生成近似时间
                                    base_time = datetime.strptime(today_str + '093200', '%Y%m%d%H%M%S')
                                    from datetime import timedelta
                                    approx_time = base_time + timedelta(seconds=processed_count * 0.5)
                                    real_timestamp = approx_time.strftime('%Y-%m-%d %H:%M:%S')

                                # 先更新参考VWAP（包含所有原始tick）
                                monitor.vwap_calculator.update_reference_vwap(test_option, price, volume)

                                # 处理这个tick，传入真实时间戳和成交量（会进行过滤）
                                monitor.process_tick_data(ContextInfo, test_option, price, real_timestamp, volume)
                                processed_count += 1
                        except Exception as e:
                            continue

                    print(f"? 成功处理{processed_count}个有效tick")

                    # 显示最终VWAP
                    final_vwap = monitor.vwap_calculator.get_current_vwap(test_option)
                    if final_vwap:
                        print(f"?? 最终VWAP: {test_option} = {final_vwap:.4f}")

                    # 显示最终统计
                    if len(monitor.data.batch_manager.batches) > 0:
                        print(f"?? 触发信号数量: {len(monitor.data.batch_manager.batches)}")
                        monitor.data.batch_manager.show_all_batches()
                    else:
                        print("?? 本次回测未触发任何信号")

                else:
                    print("? 获取的数据框为空")
            else:
                print(f"? 未获取到 {test_option} 的分笔数据")
                print("?? 可能原因:")
                print("   1. 合约代码不正确或已到期")
                print("   2. 回测日期非交易日")
                print("   3. 数据权限问题")

        except Exception as e:
            print(f"? 历史数据获取失败: {e}")
            import traceback
            traceback.print_exc()

        print("="*60)
        print("?? 回测数据处理完成")
        print("="*60)

    except Exception as e:
        print(f"? handlebar处理异常: {e}")
        logging.error(f"handlebar处理异常: {e}")
        import traceback
        traceback.print_exc()

# ==================== 工具函数 ====================
def update_parameter(param_name, new_value):
    """运行时更新参数"""
    try:
        monitor.pm.set(param_name, new_value)
        print(f"? 参数已更新: {param_name} = {new_value}")
    except Exception as e:
        print(f"? 参数更新失败: {e}")

def show_parameters():
    """显示当前参数"""
    monitor.pm.print_params()

def get_strategy_status():
    """获取策略状态"""
    selected_count = len(monitor.data.selected_options)
    total_signals = sum(len(signals) for signals in monitor.data.signals.values())
    active_trends = sum(1 for count in monitor.data.trend_count.values() if count > 0)

    print(f"?? 策略状态:")
    print(f"  - 监控合约: {selected_count} 个")
    print(f"  - 总信号数: {total_signals} 个")
    print(f"  - 活跃趋势: {active_trends} 个")

    # 显示每个合约的信号统计
    for option_code in monitor.data.selected_options:
        if option_code in monitor.data.signals:
            signals = monitor.data.signals[option_code]
            buy_count = sum(1 for s in signals if s['type'] == '买入')
            sell_count = sum(1 for s in signals if s['type'] == '卖出')
            print(f"  - {option_code}: 买入{buy_count}次, 卖出{sell_count}次")

def emergency_stop():
    """紧急停止"""
    print("?? 执行紧急停止...")
    monitor.data.selected_options.clear()
    monitor.data.trend_count.clear()
    monitor.data.trend_direction.clear()
    monitor.data.trend_prices.clear()
    print("? 策略已停止，所有状态已清空")

# ?? VWAP工具函数
def get_current_vwap(C, option_code=None):
    """获取当前VWAP价格"""
    try:
        if option_code is None:
            option_code = monitor.data.selected_options[0] if monitor.data.selected_options else None

        if option_code:
            vwap = monitor.get_current_vwap(C, option_code)
            if vwap:
                print(f"?? {option_code} 当前VWAP: {vwap:.6f}")
                return vwap
            else:
                print(f"? 无法获取 {option_code} 的VWAP")
        else:
            print("? 没有可用的期权合约")
        return None
    except Exception as e:
        print(f"? 获取VWAP失败: {e}")
        return None

def show_batch_summary():
    """显示批次汇总"""
    monitor.data.batch_manager.show_all_batches()

def show_high_low_status(option_code=None):
    """显示高低点记录状态"""
    try:
        if option_code is None:
            option_code = monitor.data.selected_options[0] if monitor.data.selected_options else None

        if option_code:
            status = monitor.vwap_pattern_analyzer.get_high_low_status(option_code)
            if status:
                print(f"\n?? {option_code} 高低点记录状态:")
                print(f"  - 当前价格: {status['current_price']:.4f}" if status['current_price'] else "  - 当前价格: 无数据")
                print(f"  - 当前VWAP: {status['current_vwap']:.4f}" if status['current_vwap'] else "  - 当前VWAP: 无数据")
                print(f"  - VWAP状态: {status['last_vwap_state']}" if status['last_vwap_state'] else "  - VWAP状态: 未确定")
                print(f"  - 高于VWAP期间历史最高点: {status['above_vwap_high']:.4f}" if status['above_vwap_high'] else "  - 高于VWAP期间历史最高点: 未记录")
                print(f"  - 低于VWAP期间历史最低点: {status['below_vwap_low']:.4f}" if status['below_vwap_low'] else "  - 低于VWAP期间历史最低点: 未记录")
                print(f"  - 上一个局部低点: {status['last_local_low']:.4f}" if status['last_local_low'] else "  - 上一个局部低点: 未记录")
                print(f"  - 是否处于反弹状态: {'是' if status['is_in_rebound'] else '否'}")
            else:
                print(f"? {option_code} 无高低点数据")
        else:
            print("? 没有可用的期权合约")
    except Exception as e:
        print(f"? 获取高低点状态失败: {e}")

def clear_all_batches():
    """清空所有批次记录"""
    try:
        monitor.data.batch_manager.batches = []
        monitor.data.batch_manager.save_batches()
        print("? 所有批次记录已清空")
    except Exception as e:
        print(f"? 清空失败: {e}")

# ==================== QMT回调函数（回测简化版本） ====================
def deal_callback(ContextInfo, dealInfo):
    """成交回调 - 回测模式不需要"""
    pass

def order_callback(ContextInfo, orderInfo):
    """委托回调 - 回测模式不需要"""
    pass

def orderError_callback(ContextInfo, orderArgs, errMsg):
    """下单异常回调 - 回测模式不需要"""
    pass

# ==================== 回测结果汇总 ====================
def show_backtest_summary():
    """显示回测结果汇总"""
    print("\n" + "="*80)
    print("?? QMT期权策略回测结果汇总")
    print("="*80)

    # 显示VWAP过滤器统计
    if monitor.vwap_filter.enabled:
        monitor.vwap_filter.print_statistics()
        print("="*80)

    batches = monitor.data.batch_manager.batches
    if not batches:
        print("?? 本次回测未触发任何信号")
        return

    # 统计信号类型
    signal_stats = {}
    for batch in batches:
        signal_type = batch.get('signal_type', 'Unknown')
        if signal_type not in signal_stats:
            signal_stats[signal_type] = 0
        signal_stats[signal_type] += 1

    print(f"?? 信号统计:")
    for signal_type, count in signal_stats.items():
        print(f"  - {signal_type}: {count}次")

    print(f"\n?? 详细记录:")
    for i, batch in enumerate(batches, 1):
        signal_type = batch.get('signal_type', 'N/A')
        print(f"{i:2d}. {batch['trade_time']} | {batch['option_code']} | "
              f"{signal_type} | 价格:{batch['entry_price']:.4f}")

    print("="*80)
    print("?? 回测完成！以上为信号触发记录，可用于策略优化分析")
    print("="*80)

# ==================== 全局变量 ====================
# 全局监控器实例
monitor = OptionMonitor()

# 在回测结束时自动显示汇总
import atexit
atexit.register(show_backtest_summary)
